// Temporary debug utility to check what categories exist in the database
import { newsService } from '@/services/firebaseService';

export const debugCategories = async () => {
  try {
    console.log('🔍 Fetching all news to check categories...');
    
    // Fetch all news without any category filter
    const allNews = await newsService.getNews(100, undefined, undefined, 'published');
    
    console.log(`📊 Total news articles: ${allNews.items.length}`);
    
    // Get all unique categories
    const categories = [...new Set(allNews.items.map(item => item.category))].filter(Boolean);
    
    console.log('📂 Categories found in database:', categories);
    
    // Show count per category
    const categoryCounts = categories.map(category => ({
      category,
      count: allNews.items.filter(item => item.category === category).length
    }));
    
    console.log('📈 Articles per category:', categoryCounts);
    
    // Show sample articles for each category
    categories.forEach(category => {
      const articles = allNews.items.filter(item => item.category === category).slice(0, 2);
      console.log(`📰 Sample articles for "${category}":`, 
        articles.map(article => ({ 
          id: article.id, 
          title: article.title.substring(0, 50) + '...' 
        }))
      );
    });
    
    return {
      totalArticles: allNews.items.length,
      categories,
      categoryCounts
    };
  } catch (error) {
    console.error('❌ Error debugging categories:', error);
    return null;
  }
};