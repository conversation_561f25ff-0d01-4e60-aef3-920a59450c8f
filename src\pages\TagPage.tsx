import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Layout } from "@/components/layout/layout";
import { NewsCardHorizontal } from "@/components/news/news-card-horizontal";
import { AdContainer } from "@/components/ads/AdContainer";
import { usePagination } from "@/hooks/usePagination";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis
} from "@/components/ui/pagination";
import { useNews } from "@/hooks/useFirebaseData";
import { useTranslatedArticles } from "@/hooks/useTranslatedContent";
import { useScrollToTopOnRouteChange } from "@/hooks/useScrollToTop";
import { detectErrorType } from "@/utils/errorUtils";
import { AdaptiveEmptyState, AdaptiveErrorState } from "@/components/ui/adaptive-empty-states";
import { DataLoader } from "@/components/ui/data-loader";
import { ProgressiveErrorBoundary } from "@/components/ui/progressive-error-boundary";
import { Badge } from "@/components/ui/badge";
import { useState, useEffect } from "react";
import { NewsArticle } from "@/types";

// Helper function to map error types
function mapErrorType(errorType: 'network' | 'server' | 'timeout' | 'general'): 'network' | 'server' | 'timeout' | 'permission' | 'notFound' | 'validation' | 'general' {
  switch (errorType) {
    case 'network':
      return 'network';
    case 'server':
      return 'server';
    case 'timeout':
      return 'timeout';
    default:
      return 'general';
  }
}

export default function TagPage() {
  const { t, i18n } = useTranslation();
  const { tag } = useParams<{ tag: string }>();
  const navigate = useNavigate();
  const currentLanguage = i18n.language as 'hi' | 'en';
  const [taggedArticles, setTaggedArticles] = useState<NewsArticle[]>([]);
  const [relatedTags, setRelatedTags] = useState<{name: string, count: number}[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Scroll to top when route changes
  useScrollToTopOnRouteChange();

  // Fetch all news and filter by tag
  const { news, loading, error } = useNews(
    undefined, // No category filter
    'published'
  );

  // Filter articles by tag
  useEffect(() => {
    if (!loading && news.items.length > 0 && tag) {
      console.log(`Filtering articles for tag: ${tag}`);
      
      // Filter articles that have the current tag
      const filteredArticles = news.items.filter(article => 
        article.tags && Array.isArray(article.tags) && 
        article.tags.some(articleTag => 
          articleTag.toLowerCase() === tag.toLowerCase()
        )
      );
      
      setTaggedArticles(filteredArticles);
      
      // Find related tags (tags that appear in the filtered articles)
      const tagCounts: Record<string, number> = {};
      filteredArticles.forEach(article => {
        if (article.tags && Array.isArray(article.tags)) {
          article.tags.forEach(articleTag => {
            if (articleTag.toLowerCase() !== tag.toLowerCase()) {
              tagCounts[articleTag] = (tagCounts[articleTag] || 0) + 1;
            }
          });
        }
      });
      
      // Convert to array and sort by count
      const sortedTags = Object.entries(tagCounts)
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10); // Get top 10 related tags
      
      setRelatedTags(sortedTags);
      setIsLoading(false);
    }
  }, [loading, news.items, tag]);

  // Progressive translation - defer translation to not block initial rendering
  const { translatedArticles, isTranslating } = useTranslatedArticles(
    taggedArticles || [], 
    true, 
    'secondary', // Tag pages are secondary content
    300 // 300ms delay to ensure page renders quickly
  );

  // Use pagination hook with 20 items per page - use translated articles
  const {
    currentPage,
    totalPages,
    currentItems,
    goToPage,
    canGoNext,
    canGoPrev,
    startIndex,
    endIndex,
    totalItems
  } = usePagination({
    data: translatedArticles,
    itemsPerPage: 20
  });

  // Show enhanced loading state with content-aware skeleton UI
  if (loading || isLoading || isTranslating) {
    return (
      <Layout>
        <div className="container py-4 sm:py-6 px-4 sm:px-6">
          {/* Header Advertisement placeholder */}
          <div className="mb-4 sm:mb-6">
            <div className="w-full h-20 bg-muted/30 rounded-lg"></div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
            <div className="lg:col-span-3">
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-800">
                  #{tag} से संबंधित समाचार लोड हो रहे हैं...
                </div>
                {Array.from({ length: 5 }).map((_, index) => (
                  <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 rounded-lg animate-pulse">
                    <div className="bg-muted rounded-md aspect-video"></div>
                    <div className="md:col-span-2 space-y-2">
                      <div className="h-4 bg-muted rounded"></div>
                      <div className="h-4 bg-muted rounded w-3/4"></div>
                      <div className="h-3 bg-muted rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="lg:col-span-1">
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="bg-muted rounded-lg h-20 animate-pulse"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  // Show adaptive error state with appropriate error type
  if (error) {
    const errorType = detectErrorType(error);
    const mappedErrorType = mapErrorType(errorType);

    return (
      <Layout>
        <div className="w-full max-w-none py-4 sm:py-6 px-2 sm:px-4 lg:px-6 xl:px-8">
          <AdaptiveErrorState
            error={error}
            errorType={mappedErrorType}
            onRetry={() => window.location.reload()}
          />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container py-4 sm:py-6 px-4 sm:px-6">
        {/* Header Advertisement */}
        <div className="mb-4 sm:mb-6">
          <AdContainer position="header" className="text-center" />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 space-y-2 sm:space-y-0">
              <h1 className="text-2xl sm:text-3xl font-bold flex items-center">
                <span className="text-primary">#</span>{tag}
              </h1>
              {totalItems > 0 && (
                <p className="text-xs sm:text-sm text-muted-foreground">
                  Showing {startIndex + 1}-{endIndex} of {totalItems} articles
                </p>
              )}
            </div>

            <DataLoader
              data={taggedArticles}
              isLoading={false}
              error={null}
              onRetry={() => window.location.reload()}
              contentType="tag"
              emptyComponent={
                <AdaptiveEmptyState
                  type="search"
                  title={`#${tag} से संबंधित कोई समाचार नहीं मिला`}
                  description="इस टैग से संबंधित कोई समाचार अभी उपलब्ध नहीं है। कृपया अन्य टैग देखें या होमपेज पर वापस जाएं।"
                  action={{ label: "होमपेज पर जाएं", href: "/" }}
                  secondaryAction={{ label: "सभी समाचार देखें", href: "/latest" }}
                />
              }
            >
              {() => (
                <ProgressiveErrorBoundary
                  onError={(error) => console.error(`Tag page error: ${error}`)}
                  onRetry={() => window.location.reload()}
                  className="mb-4"
                >
                  <>
                    <div className="grid grid-cols-1 gap-4 sm:gap-6">
                      {currentItems.map((article, index) => (
                        <div key={article.id}>
                          <NewsCardHorizontal
                            id={article.id}
                            title={article.title}
                            excerpt={article.excerpt || ""}
                            category={article.category || ""}
                            imageUrl={article.imageUrl}
                            videoUrl={article.videoUrl}
                            publishedAt={article.publishedAt}
                            createdAt={article.createdAt}
                            embedVideo={false}
                          />
                          {/* Insert content ad after every 3rd article */}
                          {(index + 1) % 3 === 0 && (
                            <div className="my-4 sm:my-6">
                              <AdContainer position="content" />
                            </div>
                          )}
                        </div>
                      ))}
                    </div>

                    {/* Pagination */}
                    {totalPages > 1 && (
                      <div className="mt-8">
                        <Pagination>
                          <PaginationContent>
                            <PaginationItem>
                              <PaginationPrevious
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  if (canGoPrev) goToPage(currentPage - 1);
                                }}
                                className={!canGoPrev ? 'pointer-events-none opacity-50' : ''}
                              />
                            </PaginationItem>

                            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => {
                              // Show first page, last page, current page, and pages around current page
                              if (
                                page === 1 ||
                                page === totalPages ||
                                (page >= currentPage - 1 && page <= currentPage + 1)
                              ) {
                                return (
                                  <PaginationItem key={page}>
                                    <PaginationLink
                                      href="#"
                                      onClick={(e) => {
                                        e.preventDefault();
                                        goToPage(page);
                                      }}
                                      isActive={currentPage === page}
                                    >
                                      {page}
                                    </PaginationLink>
                                  </PaginationItem>
                                );
                              } else if (
                                page === currentPage - 2 ||
                                page === currentPage + 2
                              ) {
                                return (
                                  <PaginationItem key={page}>
                                    <PaginationEllipsis />
                                  </PaginationItem>
                                );
                              }
                              return null;
                            })}

                            <PaginationItem>
                              <PaginationNext
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  if (canGoNext) goToPage(currentPage + 1);
                                }}
                                className={!canGoNext ? 'pointer-events-none opacity-50' : ''}
                              />
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    )}
                  </>
                </ProgressiveErrorBoundary>
              )}
            </DataLoader>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 mt-6 lg:mt-0">
            <div className="sticky top-6 space-y-4 sm:space-y-6">
              {/* Related Tags */}
              {relatedTags.length > 0 && (
                <div className="bg-muted/20 p-4 rounded-lg">
                  <h3 className="font-bold mb-3">संबंधित टैग</h3>
                  <div className="flex flex-wrap gap-2">
                    {relatedTags.map(relatedTag => (
                      <Badge 
                        key={relatedTag.name} 
                        variant="secondary" 
                        className="hover:bg-primary hover:text-white transition-colors cursor-pointer"
                        onClick={() => navigate(`/tag/${relatedTag.name}`)}
                      >
                        #{relatedTag.name} ({relatedTag.count})
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Advertisements */}
              <AdContainer position="sidebar" maxAds={3} />
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}