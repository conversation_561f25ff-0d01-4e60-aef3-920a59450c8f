import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';

// Enhanced skeleton for featured news section with better perceived performance
export const FeaturedNewsSkeleton: React.FC = () => {
  return (
    <div className="w-full overflow-hidden rounded-lg border bg-card transition-all">
      <div className="relative">
        <Skeleton className="w-full h-64 sm:h-80 lg:h-96" />
        <div className="absolute top-2 sm:top-4 left-2 sm:left-4">
          <Skeleton className="h-6 w-20 rounded-md" />
        </div>
        {/* Enhanced shimmer effect overlay for better perceived performance */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer opacity-60"></div>
      </div>
      <div className="p-3 sm:p-4 space-y-3">
        <Skeleton className="h-7 sm:h-8 w-3/4" />
        <Skeleton className="h-4 sm:h-5 w-full" />
        <Skeleton className="h-4 sm:h-5 w-2/3" />
        <div className="flex items-center space-x-3 sm:space-x-4 pt-2">
          <div className="flex items-center">
            <Skeleton className="h-4 w-4 mr-1 rounded-full" />
            <Skeleton className="h-4 w-20" />
          </div>
          <div className="flex items-center">
            <Skeleton className="h-4 w-4 mr-1 rounded-full" />
            <Skeleton className="h-4 w-8" />
          </div>
        </div>
      </div>
    </div>
  );
};

// Enhanced skeleton for news card small (sidebar news) with better perceived performance
export const NewsCardSmallSkeleton: React.FC = () => {
  return (
    <div className="grid grid-cols-3 gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg hover:bg-muted/50 transition-colors">
      <div className="relative overflow-hidden rounded-md">
        <Skeleton className="aspect-square" />
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer opacity-60"></div>
      </div>
      <div className="col-span-2 space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-3 w-1/2 mt-auto" />
      </div>
    </div>
  );
};

// Enhanced skeleton for trending tags with staggered animation
export const TrendingTagsSkeleton: React.FC = () => {
  return (
    <div className="flex flex-wrap gap-2">
      {Array.from({ length: 6 }).map((_, index) => (
        <div key={index} className="relative overflow-hidden">
          <Skeleton 
            className="h-6 w-16 rounded-full" 
            style={{ animationDelay: `${index * 100}ms` }}
          />
          <div 
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer opacity-50 rounded-full"
            style={{ animationDelay: `${index * 100}ms` }}
          ></div>
        </div>
      ))}
    </div>
  );
};

// Enhanced skeleton for category tabs with staggered animation
export const CategoryTabsSkeleton: React.FC = () => {
  return (
    <div className="border-b overflow-x-auto">
      <div className="flex space-x-4 p-1">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="relative overflow-hidden">
            <Skeleton 
              className="h-8 w-20 rounded" 
              style={{ animationDelay: `${index * 50}ms` }}
            />
            <div 
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/25 to-transparent animate-shimmer opacity-50 rounded"
              style={{ animationDelay: `${index * 50}ms` }}
            ></div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Enhanced skeleton for category content with staggered animation
export const CategoryContentSkeleton: React.FC = () => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 pt-4 sm:pt-6">
      {Array.from({ length: 4 }).map((_, index) => (
        <div 
          key={index} 
          className="border rounded-lg overflow-hidden bg-card transition-all hover:shadow-md"
          style={{ animationDelay: `${index * 100}ms` }}
        >
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
            <div className="sm:col-span-1 relative overflow-hidden">
              <Skeleton className="h-48 sm:h-full" />
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer opacity-60"></div>
            </div>
            <div className="p-3 sm:p-4 sm:col-span-2 space-y-2">
              <Skeleton className="h-5 sm:h-6 w-4/5" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
              <Skeleton className="h-3 w-1/4 mt-2" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

// Enhanced skeleton for videos section with staggered animation
export const VideosSkeleton: React.FC = () => {
  return (
    <div className="space-y-4">
      <Skeleton className="h-8 w-48 animate-pulse" />
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <div 
            key={index} 
            className="relative overflow-hidden rounded-lg"
            style={{ animationDelay: `${index * 150}ms` }}
          >
            <Skeleton className="aspect-video animate-pulse" />
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer"></div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Enhanced skeleton for photo gallery with staggered animation
export const PhotoGallerySkeleton: React.FC = () => {
  return (
    <div className="space-y-4">
      <Skeleton className="h-8 w-48 animate-pulse" />
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <div 
            key={index} 
            className="relative overflow-hidden rounded-lg"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <Skeleton className="aspect-square animate-pulse" />
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer"></div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Enhanced skeleton for popular articles with staggered animation
export const PopularArticlesSkeleton: React.FC = () => {
  return (
    <div className="space-y-4">
      <Skeleton className="h-7 w-40 animate-pulse" />
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <div 
            key={index} 
            className="space-y-2"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <div className="flex items-start gap-4">
              <Skeleton className="rounded-full w-8 h-8 flex-shrink-0 animate-pulse" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-full animate-pulse" />
                <Skeleton className="h-4 w-4/5 animate-pulse" />
                <div className="flex justify-between">
                  <Skeleton className="h-3 w-20 animate-pulse" />
                  <Skeleton className="h-3 w-16 animate-pulse" />
                </div>
              </div>
            </div>
            {index < 4 && <Skeleton className="h-px w-full mt-4 animate-pulse" />}
          </div>
        ))}
      </div>
    </div>
  );
};

// Enhanced progressive loading skeleton for the homepage with better perceived performance
export const ProgressiveHomeSkeleton: React.FC<{
  phase: 'critical' | 'secondary' | 'enhancement';
}> = ({ phase }) => {
  return (
    <div className="w-full max-w-none py-4 sm:py-6 px-2 sm:px-4 lg:px-6 xl:px-8">
      <style jsx>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        .animate-shimmer {
          animation: shimmer 2s infinite;
        }
      `}</style>
      
      <div className="space-y-6 sm:space-y-8">
        {/* Phase indicator with enhanced styling */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 text-sm text-blue-800 flex items-center">
          <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
          Loading Phase: {phase} - {
            phase === 'critical' ? 'महत्वपूर्ण सामग्री लोड हो रही है...' :
            phase === 'secondary' ? 'अतिरिक्त सामग्री लोड हो रही है...' :
            'मीडिया सामग्री लोड हो रही है...'
          }
        </div>
        
        {/* Hero Section */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 sm:gap-6 mb-8 sm:mb-10">
          {/* Main Featured Article */}
          <div className="lg:col-span-8">
            <FeaturedNewsSkeleton />
          </div>

          {/* Secondary Articles */}
          <div className="lg:col-span-4 space-y-4 sm:space-y-6">
            <div className="flex items-center justify-between mb-2">
              <Skeleton className="h-6 w-32 animate-pulse" />
              <Skeleton className="h-4 w-20 animate-pulse" />
            </div>
            
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, index) => (
                <div key={index} style={{ animationDelay: `${index * 100}ms` }}>
                  <NewsCardSmallSkeleton />
                </div>
              ))}
            </div>
            
            {phase !== 'critical' && (
              <div>
                <div className="mb-2">
                  <Skeleton className="h-6 w-24 animate-pulse" />
                </div>
                <TrendingTagsSkeleton />
              </div>
            )}
          </div>
        </div>
        
        {/* Category News Tabs - Only show in secondary and enhancement phases */}
        {phase !== 'critical' && (
          <div className="mb-8 sm:mb-10">
            <CategoryTabsSkeleton />
            <CategoryContentSkeleton />
          </div>
        )}
        
        {/* Videos Section - Only show in enhancement phase */}
        {phase === 'enhancement' && (
          <div className="mb-10">
            <VideosSkeleton />
          </div>
        )}
        
        {/* Two Column Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8 mb-8 sm:mb-10">
          <div className="lg:col-span-2 space-y-6 sm:space-y-8">
            {/* More News */}
            <div>
              <Skeleton className="h-7 w-40 mb-4 animate-pulse" />
              <div className="space-y-4 sm:space-y-6">
                {Array.from({ length: 3 }).map((_, index) => (
                  <div 
                    key={index} 
                    className="border rounded-lg overflow-hidden bg-card transition-all hover:shadow-md"
                    style={{ animationDelay: `${index * 150}ms` }}
                  >
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
                      <div className="sm:col-span-1 relative overflow-hidden">
                        <Skeleton className="h-48 sm:h-full animate-pulse" />
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer"></div>
                      </div>
                      <div className="p-3 sm:p-4 sm:col-span-2 space-y-2">
                        <Skeleton className="h-5 sm:h-6 w-4/5 animate-pulse" />
                        <Skeleton className="h-4 w-full animate-pulse" />
                        <Skeleton className="h-4 w-2/3 animate-pulse" />
                        <Skeleton className="h-3 w-1/4 mt-2 animate-pulse" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Photo Gallery - Only show in enhancement phase */}
            {phase === 'enhancement' && (
              <div className="mt-6 sm:mt-8">
                <PhotoGallerySkeleton />
              </div>
            )}
          </div>
          
          <div className="space-y-8">
            {/* Sidebar Advertisement */}
            <div className="relative overflow-hidden rounded-lg">
              <Skeleton className="h-60 w-full animate-pulse" />
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer"></div>
            </div>

            {/* Popular Articles - Only show in secondary and enhancement phases */}
            {phase !== 'critical' && (
              <PopularArticlesSkeleton />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};