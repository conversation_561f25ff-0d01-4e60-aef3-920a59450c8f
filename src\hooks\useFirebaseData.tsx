import { useState, useCallback, useEffect } from 'react';
import { adService, userService, videoService, galleryService, breakingNewsService } from '@/services/firebaseService';
import { NewsArticle, Advertisement, User, Video, GalleryImage, BreakingNews } from '@/types';
import {
  useNewsQuery,
  useNewsArticleQuery,
  useFeaturedNewsQuery,
  useCriticalContentQuery,
  useNewsByCategoriesQuery,
  useTrendingTagsQuery,
  useMostViewedNewsQuery,
  useMostViewedNewsThisWeekQuery,
  useLatestNewsByDateQuery,
  useVideosQuery,
  useGalleryImagesQuery,
  useBreakingNewsQuery,
  useAdsQuery,
  useAllAdsQuery,
  useUsersQuery,
  useAllBreakingNewsQuery,
  usePrefetchCategoryNews,
  useCreateNewsMutation,
  useUpdateNewsMutation,
  useDeleteNewsMutation
} from './useReactQueryData';

// Hook for news data - now using React Query for optimal caching
export const useNews = (category?: string, status: 'published' | 'draft' | 'all' = 'published', state?: string, initialLimit: number = 5) => {
  const { data: news, isLoading: loading, error: queryError, refetch } = useNewsQuery(category, status, state, initialLimit);
  
  // Transform error to match existing interface
  const error = queryError ? (queryError instanceof Error ? queryError.message : 'समाचार लोड करने में त्रुटि') : null;
  
  // Provide default data structure to maintain compatibility
  const newsData = news || {
    items: [],
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: initialLimit
  };

  return { 
    news: newsData, 
    loading, 
    error, 
    refetch: () => refetch() 
  };
};

// Hook for single news article - now using React Query for optimal caching
export const useNewsArticle = (id: string) => {
  const { data: article, isLoading: loading, error: queryError } = useNewsArticleQuery(id);
  
  // Transform error to match existing interface
  const error = queryError ? (queryError instanceof Error ? queryError.message : 'लेख लोड करने में त्रुटि') : null;

  return { article: article || null, loading, error };
};

// Hook for featured news - now using React Query for optimal caching
export const useFeaturedNews = (limit: number = 1) => {
  const { data: featuredNews, isLoading: loading, error: queryError } = useFeaturedNewsQuery(limit);
  
  // Transform error to match existing interface
  const error = queryError ? (queryError instanceof Error ? queryError.message : 'मुख्य समाचार लोड करने में त्रुटि') : null;

  return { featuredNews: featuredNews || [], loading, error };
};

// Hook for parallel loading of critical content (news and featured news) - now using React Query
export const useCriticalContent = (newsLimit: number = 5, featuredLimit: number = 1) => {
  const { data: criticalContent, isLoading: loading, error: queryError } = useCriticalContentQuery(newsLimit, featuredLimit);
  
  // Transform error to match existing interface
  const error = queryError ? (queryError instanceof Error ? queryError.message : 'महत्वपूर्ण सामग्री लोड करने में त्रुटि') : null;
  
  // Provide default data structure to maintain compatibility
  const defaultContent = {
    news: {
      items: [],
      currentPage: 1,
      totalPages: 1,
      totalItems: 0,
      itemsPerPage: newsLimit
    },
    featuredNews: []
  };

  const content = criticalContent || defaultContent;

  return { 
    news: content.news, 
    featuredNews: content.featuredNews, 
    loading, 
    error 
  };
};

// Hook for news by categories (for home page tabs) - now using React Query for optimal caching
export const useNewsByCategories = (enabled: boolean = true) => {
  const { data: categoriesData, isLoading: loading, error: queryError } = useNewsByCategoriesQuery(enabled);
  
  // Transform error to match existing interface
  const error = queryError ? (queryError instanceof Error ? queryError.message : 'श्रेणीबद्ध समाचार लोड करने में त्रुटि') : null;
  
  // Provide default data structure to maintain compatibility
  const categories = categoriesData?.categories || [];
  const categoriesLoaded = !!categoriesData;
  
  // Load cached category data from localStorage
  const loadCachedCategoryData = () => {
    if (typeof window === 'undefined') return {}; // SSR check
    
    try {
      const cachedData = localStorage.getItem('categoryNewsCache');
      if (cachedData) {
        const parsedData = JSON.parse(cachedData);
        // Check if cache is still valid (less than 30 minutes old)
        const cacheTime = parsedData.timestamp || 0;
        const currentTime = new Date().getTime();
        const cacheAge = currentTime - cacheTime;
        const cacheMaxAge = 30 * 60 * 1000; // 30 minutes
        
        if (cacheAge < cacheMaxAge) {
          console.log('📦 Using cached category data from localStorage');
          return parsedData.categorizedNews || {};
        } else {
          console.log('🕒 Cache expired, fetching fresh data');
          try {
            localStorage.removeItem('categoryNewsCache');
          } catch (e) {
            console.error('Error removing expired cache:', e);
          }
          return {};
        }
      }
    } catch (err) {
      console.error('Error loading cached category data:', err);
    }
    return {};
  };
  
  // State for tracking individual category loading states and data
  const [loadingCategories, setLoadingCategories] = useState<Record<string, boolean>>({});
  const [categorizedNews, setCategorizedNews] = useState<Record<string, NewsArticle[]>>(loadCachedCategoryData());

  // Save category data to localStorage whenever it changes
  useEffect(() => {
    if (Object.keys(categorizedNews).length > 0) {
      try {
        const cacheData = {
          categorizedNews,
          timestamp: new Date().getTime()
        };
        localStorage.setItem('categoryNewsCache', JSON.stringify(cacheData));
        console.log('💾 Saved category data to localStorage');
      } catch (err) {
        console.error('Error saving category data to cache:', err);
      }
    }
  }, [categorizedNews]);

  console.log('🔍 useNewsByCategories Debug:',
    `enabled: ${enabled}, ` +
    `categories: ${categories.length}, ` +
    `categoriesLoaded: ${categoriesLoaded}, ` +
    `categorizedNewsKeys: [${Object.keys(categorizedNews).join(', ')}], ` +
    `loadingCategories: ${JSON.stringify(loadingCategories)}`
  );
  
  // Function to fetch news for a specific category on-demand
  const fetchCategoryNews = useCallback(async (category: string) => {
    console.log(`🚀 fetchCategoryNews called for category: ${category}`);
    
    // Skip if already loaded or loading
    if (categorizedNews[category]?.length > 0 || loadingCategories[category]) {
      console.log(`✅ Category ${category} already loaded (${categorizedNews[category]?.length || 0} articles) or loading, skipping fetch`);
      return;
    }
    
    try {
      console.log(`📡 Fetching news for category: ${category}`);
      setLoadingCategories(prev => ({ ...prev, [category]: true }));
      
      // Import newsService directly to fetch category news
      const { newsService } = await import('@/services/firebaseService');
      
      // Fetch news for this category
      const data = await newsService.getNews(6, undefined, category, 'published');
      
      console.log(`📊 Fetched ${data.items.length} articles for category ${category}:`,
        data.items.map(item => `${item.id}: ${item.title.substring(0, 50)}... (${item.category})`).join(', ')
      );
      
      // Update the categorized news state
      setCategorizedNews(prev => ({
        ...prev,
        [category]: data.items
      }));
      
    } catch (categoryError) {
      console.error(`❌ Error fetching news for category ${category}:`, categoryError);
    } finally {
      setLoadingCategories(prev => ({ ...prev, [category]: false }));
    }
  }, [categorizedNews, loadingCategories]);

  // Prefetch function for hover effects
  const prefetchCategoryNews = async (category: string) => {
    if (!categorizedNews[category]?.length && !loadingCategories[category]) {
      console.log(`🔄 Prefetching category ${category} in the background`);
      await fetchCategoryNews(category);
    }
  };

  return { 
    categorizedNews, 
    loading, 
    loadingCategories,
    error, 
    categories,
    categoriesLoaded,
    fetchCategoryNews,
    prefetchCategoryNews
  };
};

// Hook for trending tags - now using React Query for optimal caching
export const useTrendingTags = (enabled: boolean = true, delay: number = 0) => {
  const { data: trendingTags, isLoading: loading, error: queryError, refetch } = useTrendingTagsQuery(enabled, delay);
  
  // Transform error to match existing interface
  const error = queryError ? (queryError instanceof Error ? queryError.message : 'ट्रेंडिंग टैग लोड करने में त्रुटि') : null;

  // Manual fetch function that can be called to load trending tags on demand
  const fetchTrendingTags = async () => {
    if (!enabled || loading) return;
    await refetch();
  };

  return { trendingTags: trendingTags || [], loading, error, fetchTrendingTags };
};

// Hook for most viewed articles - now using React Query for optimal caching
export const useMostViewedNews = (limit: number = 5, enabled: boolean = true) => {
  const { data: mostViewedNews, isLoading: loading, error: queryError } = useMostViewedNewsQuery(limit, enabled);

  // Transform error to match existing interface
  const error = queryError ? (queryError instanceof Error ? queryError.message : 'सबसे लोकप्रिय समाचार लोड करने में त्रुटि') : null;

  return { mostViewedNews: mostViewedNews || [], loading, error };
};

// Hook for most viewed articles this week - using React Query for optimal caching
export const useMostViewedNewsThisWeek = (limit: number = 10, enabled: boolean = true) => {
  const { data: mostViewedNews, isLoading: loading, error: queryError } = useMostViewedNewsThisWeekQuery(limit, enabled);

  // Transform error to match existing interface
  const error = queryError ? (queryError instanceof Error ? queryError.message : 'इस सप्ताह के लोकप्रिय समाचार लोड करने में त्रुटि') : null;

  return { mostViewedNews: mostViewedNews || [], loading, error };
};

// Hook for latest news by date (today/yesterday) - now using React Query for optimal caching
export const useLatestNewsByDate = (limit: number = 15, category?: string, enabled: boolean = true) => {
  const { data: latestNews, isLoading: loading, error: queryError } = useLatestNewsByDateQuery(limit, category, enabled);
  
  // Transform error to match existing interface
  const error = queryError ? (queryError instanceof Error ? queryError.message : 'आज की ताज़ा खबरें लोड करने में त्रुटि') : null;

  return { latestNews: latestNews || [], loading, error };
};

// Hook for videos - now using React Query for optimal caching
export const useVideos = (enabled: boolean = true) => {
  const { data: videos, isLoading: loading, error: queryError } = useVideosQuery(enabled);
  
  // Transform error to match existing interface
  const error = queryError ? (queryError instanceof Error ? queryError.message : 'वीडियो लोड करने में त्रुटि') : null;

  return { videos: videos || [], loading, error };
};

// Hook for gallery images - now using React Query for optimal caching
export const useGalleryImages = (enabled: boolean = true) => {
  const { data: images, isLoading: loading, error: queryError } = useGalleryImagesQuery(enabled);
  
  // Transform error to match existing interface
  const error = queryError ? (queryError instanceof Error ? queryError.message : 'गैलरी इमेज लोड करने में त्रुटि') : null;

  return { images: images || [], loading, error };
};

// Hook for breaking news - now using React Query for optimal caching
export const useBreakingNews = () => {
  const { data: breakingNews, isLoading: loading, error: queryError } = useBreakingNewsQuery();
  
  // Transform error to match existing interface
  const error = queryError ? (queryError instanceof Error ? queryError.message : 'ब्रेकिंग न्यूज़ लोड करने में त्रुटि') : null;

  return { breakingNews: breakingNews || [], loading, error };
};

// Hook for all breaking news (admin) - now using React Query for optimal caching
export const useAllBreakingNews = () => {
  const { data: breakingNews, isLoading: loading, error: queryError, refetch } = useAllBreakingNewsQuery();
  
  // Transform error to match existing interface
  const error = queryError ? (queryError instanceof Error ? queryError.message : 'Failed to fetch breaking news') : null;

  return { breakingNews: breakingNews || [], loading, error, refetch };
};

// Hook for advertisements - now using React Query for optimal caching
export const useAds = (position: Advertisement['position'], category?: string) => {
  const { data: ads, isLoading: loading, error: queryError } = useAdsQuery(position, category);
  
  // Transform error to match existing interface
  const error = queryError ? (queryError instanceof Error ? queryError.message : 'Failed to fetch ads') : null;

  return { ads: ads || [], loading, error };
};

// Hook for all advertisements (admin) - now using React Query for optimal caching
export const useAllAds = (enabled: boolean = true) => {
  const { data: ads, isLoading: loading, error: queryError, refetch } = useAllAdsQuery(enabled);
  
  // Transform error to match existing interface
  const error = queryError ? (queryError instanceof Error ? queryError.message : 'Failed to fetch ads') : null;

  return { ads: ads || [], loading, error, refetch };
};

// Hook for users (admin) - now using React Query for optimal caching
export const useUsers = () => {
  const { data: users, isLoading: loading, error: queryError, refetch } = useUsersQuery();
  
  // Transform error to match existing interface
  const error = queryError ? (queryError instanceof Error ? queryError.message : 'Failed to fetch users') : null;

  return { users: users || [], loading, error, refetch };
};

// Hook for CRUD operations - now using React Query mutations for optimal cache invalidation
export const useFirebaseCRUD = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize React Query mutations
  const createNewsMutation = useCreateNewsMutation();
  const updateNewsMutation = useUpdateNewsMutation();
  const deleteNewsMutation = useDeleteNewsMutation();

  const executeOperation = async <T,>(operation: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await operation();
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Operation failed';
      setError(errorMessage);
      console.error('CRUD operation error:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading: loading || createNewsMutation.isPending || updateNewsMutation.isPending || deleteNewsMutation.isPending,
    error: error || 
           (createNewsMutation.error ? createNewsMutation.error.message : null) ||
           (updateNewsMutation.error ? updateNewsMutation.error.message : null) ||
           (deleteNewsMutation.error ? deleteNewsMutation.error.message : null),
    
    // News operations - using React Query mutations for automatic cache invalidation
    createNews: async (article: Omit<NewsArticle, 'id' | 'createdAt' | 'updatedAt'>) => {
      try {
        const result = await createNewsMutation.mutateAsync(article);
        return result;
      } catch (err) {
        console.error('Error creating news:', err);
        return null;
      }
    },
    updateNews: async (id: string, updates: Partial<NewsArticle>) => {
      try {
        const result = await updateNewsMutation.mutateAsync({ id, updates });
        return result;
      } catch (err) {
        console.error('Error updating news:', err);
        return null;
      }
    },
    deleteNews: async (id: string) => {
      try {
        const result = await deleteNewsMutation.mutateAsync(id);
        return result;
      } catch (err) {
        console.error('Error deleting news:', err);
        return null;
      }
    },
    
    // Ad operations - keeping existing implementation for now
    createAd: (ad: Omit<Advertisement, 'id' | 'createdAt' | 'updatedAt' | 'clickCount' | 'impressionCount'>) =>
      executeOperation(() => adService.createAd(ad)),
    updateAd: (id: string, updates: Partial<Advertisement>) =>
      executeOperation(() => adService.updateAd(id, updates)),
    deleteAd: (id: string) =>
      executeOperation(() => adService.deleteAd(id)),
    trackAdImpression: (id: string) =>
      executeOperation(() => adService.trackImpression(id)),
    trackAdClick: (id: string) =>
      executeOperation(() => adService.trackClick(id)),
    
    // User operations - keeping existing implementation for now
    updateUserRole: (id: string, role: User['role']) =>
      executeOperation(() => userService.updateUserRole(id, role)),
    updateUser: (id: string, updates: Partial<User>) =>
      executeOperation(() => userService.updateUser(id, updates)),
    deleteUser: (id: string) =>
      executeOperation(() => userService.deleteUser(id)),

    // Video operations - keeping existing implementation for now
    createVideo: (video: Omit<Video, 'id' | 'createdAt' | 'updatedAt'>) =>
      executeOperation(() => videoService.createVideo(video)),
    updateVideo: (id: string, updates: Partial<Video>) =>
      executeOperation(() => videoService.updateVideo(id, updates)),
    deleteVideo: (id: string) =>
      executeOperation(() => videoService.deleteVideo(id)),

    // Gallery operations - keeping existing implementation for now
    createImage: (image: Omit<GalleryImage, 'id' | 'createdAt' | 'updatedAt'>) =>
      executeOperation(() => galleryService.createImage(image)),
    updateImage: (id: string, updates: Partial<GalleryImage>) =>
      executeOperation(() => galleryService.updateImage(id, updates)),
    deleteImage: (id: string) =>
      executeOperation(() => galleryService.deleteImage(id)),

    // Breaking news operations - keeping existing implementation for now
    createBreakingNews: (breakingNews: Omit<BreakingNews, 'id' | 'createdAt' | 'updatedAt'>) =>
      executeOperation(() => breakingNewsService.createBreakingNews(breakingNews)),
    updateBreakingNews: (id: string, updates: Partial<BreakingNews>) =>
      executeOperation(() => breakingNewsService.updateBreakingNews(id, updates)),
    deleteBreakingNews: (id: string) =>
      executeOperation(() => breakingNewsService.deleteBreakingNews(id))
  };
};
