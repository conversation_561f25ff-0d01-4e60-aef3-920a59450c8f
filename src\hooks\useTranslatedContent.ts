import { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { translationService } from '@/services/translationService';
import { NewsArticle } from '@/types';

// Translation cache to prevent repeated translations
const translationCache = new Map<string, NewsArticle>();

// Helper function to create cache key
const createCacheKey = (articleId: string, language: string) => `${articleId}_${language}`;

// Hook to translate article content based on current language with progressive loading support
export const useTranslatedArticle = (
  article: NewsArticle | null,
  priority: 'critical' | 'secondary' | 'enhancement' = 'secondary',
  delay: number = 0
) => {
  const { i18n } = useTranslation();
  const [translatedArticle, setTranslatedArticle] = useState<NewsArticle | null>(article);
  const [isTranslating, setIsTranslating] = useState(false);
  const [translationStarted, setTranslationStarted] = useState(false);

  // Memoize cache key to prevent unnecessary recalculations
  const cacheKey = useMemo(() =>
    article ? createCacheKey(article.id, i18n.language) : null,
    [article?.id, i18n.language]
  );

  // For critical content, show original article immediately without waiting for translation
  // For non-critical content, defer translation processing
  const shouldTranslate = useMemo(() => {
    if (!article) return false;
    if (priority === 'critical') return false; // Don't translate critical content initially
    return true;
  }, [article, priority]);

  // Memoize the translateArticle function to prevent unnecessary re-renders
  const translateArticle = useCallback(async () => {
    if (!shouldTranslate || translationStarted) {
      return;
    }

    if (!article || !cacheKey) {
      setTranslatedArticle(null);
      return;
    }

    setTranslationStarted(true);

    // Add delay for non-critical content to ensure it doesn't block initial rendering
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    // Check cache first
    const cached = translationCache.get(cacheKey);
    if (cached) {
      setTranslatedArticle(cached);
      return;
    }

    setIsTranslating(true);
    try {
      const currentLang = i18n.language as 'hi' | 'en';
      const translated = await translationService.translateArticle(article, currentLang);

      // Cache the result
      translationCache.set(cacheKey, translated);
      setTranslatedArticle(translated);
    } catch (error) {
      console.error('Translation failed:', error);
      setTranslatedArticle(article); // Fallback to original
    } finally {
      setIsTranslating(false);
    }
  }, [article, cacheKey, i18n.language, shouldTranslate, delay, translationStarted]);

  // Start translation after a delay for non-critical content
  useEffect(() => {
    if (shouldTranslate && !translationStarted) {
      const timer = setTimeout(() => {
        translateArticle();
      }, delay);
      return () => clearTimeout(timer);
    }
  }, [translateArticle, shouldTranslate, delay, translationStarted]);

  // Always return original article immediately for critical content
  // Return translated article for non-critical content once translation is complete
  const finalArticle = useMemo(() => {
    if (priority === 'critical') {
      return article; // Always show original for critical content
    }
    return translatedArticle;
  }, [priority, article, translatedArticle]);

  return { 
    translatedArticle: finalArticle, 
    isTranslating: priority === 'critical' ? false : isTranslating 
  };
};

// Hook to translate array of articles with progressive loading support
export const useTranslatedArticles = (
  articles: NewsArticle[], 
  enabled: boolean = true,
  priority: 'critical' | 'secondary' | 'enhancement' = 'secondary',
  delay: number = 0
) => {
  const { i18n } = useTranslation();
  const [translatedArticles, setTranslatedArticles] = useState<NewsArticle[]>(articles);
  const [isTranslating, setIsTranslating] = useState(false);
  const [translationStarted, setTranslationStarted] = useState(false);
  const [enableCriticalTranslation, setEnableCriticalTranslation] = useState(false);

  // Memoize articles to prevent unnecessary re-translations
  // Use a more efficient approach to memoize based on article IDs
  const articleIds = useMemo(() => 
    articles.map(a => a.id).join(','),
    [articles]
  );
  
  const memoizedArticles = useMemo(() => articles, [articleIds]);

  // For critical content, show original articles initially, but allow progressive translation
  // For non-critical content, defer translation processing
  const shouldTranslate = useMemo(() => {
    if (!enabled) return false;
    if (priority === 'critical' && !enableCriticalTranslation) return false;
    return true;
  }, [enabled, priority, enableCriticalTranslation]);

  // Function to enable translation for critical content after initial render
  const enableProgressiveTranslation = useCallback(() => {
    if (priority === 'critical' && !enableCriticalTranslation) {
      setEnableCriticalTranslation(true);
    }
  }, [priority, enableCriticalTranslation]);

  // Memoize the translateArticles function to prevent unnecessary re-renders
  const translateArticles = useCallback(async () => {
    if (!shouldTranslate || translationStarted) {
      return;
    }
    
    if (!memoizedArticles || memoizedArticles.length === 0) {
      setTranslatedArticles([]);
      return;
    }

    setTranslationStarted(true);
    
    // Add delay for non-critical content to ensure it doesn't block initial rendering
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    
    setIsTranslating(true);
    try {
      const currentLang = i18n.language as 'hi' | 'en';
      const results: NewsArticle[] = [];

      // Process articles in batches to prevent overwhelming the system
      for (const article of memoizedArticles) {
        const cacheKey = createCacheKey(article.id, currentLang);
        const cached = translationCache.get(cacheKey);

        if (cached) {
          results.push(cached);
        } else {
          try {
            const translated = await translationService.translateArticle(article, currentLang);
            translationCache.set(cacheKey, translated);
            results.push(translated);
          } catch (error) {
            console.error(`Translation failed for article ${article.id}:`, error);
            results.push(article); // Fallback to original
          }
        }
      }

      setTranslatedArticles(results);
    } catch (error) {
      console.error('Translation failed:', error);
      setTranslatedArticles(memoizedArticles); // Fallback to original
    } finally {
      setIsTranslating(false);
    }
  }, [memoizedArticles, i18n.language, shouldTranslate, delay, translationStarted]);

  // Start translation after a delay for non-critical content or when enabled for critical content
  useEffect(() => {
    if (shouldTranslate && !translationStarted) {
      console.log(`🌐 Starting translation for ${priority} content with ${delay}ms delay`);
      const timer = setTimeout(() => {
        translateArticles();
      }, delay);
      return () => clearTimeout(timer);
    }
  }, [translateArticles, shouldTranslate, delay, translationStarted, priority]);

  // Always return original articles initially for critical content
  // Return translated articles for non-critical content once translation is complete
  const finalArticles = useMemo(() => {
    if (priority === 'critical' && !enableCriticalTranslation) {
      return memoizedArticles; // Show original for critical content initially
    }
    return translatedArticles;
  }, [priority, enableCriticalTranslation, memoizedArticles, translatedArticles]);

  return { 
    translatedArticles: finalArticles, 
    isTranslating: (priority === 'critical' && !enableCriticalTranslation) ? false : isTranslating,
    enableProgressiveTranslation
  };
};

// Hook for translating single text
export const useTranslatedText = (text: string) => {
  const { i18n } = useTranslation();
  const [translatedText, setTranslatedText] = useState(text);
  const [isTranslating, setIsTranslating] = useState(false);

  // Memoize the translateText function to prevent unnecessary re-renders
  const translateText = useCallback(async () => {
    if (!text) {
      setTranslatedText('');
      return;
    }
    
    setIsTranslating(true);
    try {
      const currentLang = i18n.language as 'hi' | 'en';
      const sourceLang = currentLang === 'hi' ? 'en' : 'hi';
      const translated = await translationService.translateText(text, currentLang, sourceLang);
      setTranslatedText(translated);
    } catch (error) {
      console.error('Translation failed:', error);
      setTranslatedText(text); // Fallback to original
    } finally {
      setIsTranslating(false);
    }
  }, [text, i18n.language]);

  useEffect(() => {
    translateText();
  }, [translateText]);

  return { translatedText, isTranslating };
};
