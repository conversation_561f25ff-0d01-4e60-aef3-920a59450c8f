import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Layout } from '@/components/layout/layout';
import { NewsCardHorizontal } from '@/components/news/news-card-horizontal';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Search, Loader2 } from 'lucide-react';
import { searchService, SearchResult } from '@/services/searchService';
import { useTranslatedArticles } from '@/hooks/useTranslatedContent';
import { detectErrorType } from '@/utils/errorUtils';
import { AdaptiveEmptyState, AdaptiveErrorState } from '@/components/ui/adaptive-empty-states';
import { DataLoader } from '@/components/ui/data-loader';
import { ProgressiveErrorBoundary } from '@/components/ui/progressive-error-boundary';

// Helper function to map error types
function mapErrorType(errorType: 'network' | 'server' | 'timeout' | 'general'): 'network' | 'server' | 'timeout' | 'permission' | 'notFound' | 'validation' | 'general' {
  switch (errorType) {
    case 'network':
      return 'network';
    case 'server':
      return 'server';
    case 'timeout':
      return 'timeout';
    default:
      return 'general';
  }
}

export default function SearchResults() {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState(searchParams.get('q') || '');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);

  // Progressive translation for search results - defer translation to not block initial rendering
  const { translatedArticles: translatedResults, isTranslating } = useTranslatedArticles(
    results.map(result => ({
      id: result.id,
      title: result.title,
      excerpt: result.excerpt,
      category: result.category,
      imageUrl: result.imageUrl,
      publishedAt: result.publishedAt,
      content: '',
      author: '',
      featured: false,
      status: 'published' as const,
      tags: [],
      commentsCount: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    })),
    true,
    'secondary', // Search results are secondary content
    200 // 200ms delay to ensure search results render quickly
  );

  const performSearch = async (term: string) => {
    if (!term.trim()) return;

    setLoading(true);
    setHasSearched(true);
    setSearchError(null);
    
    try {
      const searchResults = await searchService.searchArticles(term, 20);
      setResults(searchResults);
      
      // Update URL with search term
      setSearchParams({ q: term });
    } catch (error) {
      console.error('Search error:', error);
      setResults([]);
      // Handle error with simple error handling
      setSearchError(error instanceof Error ? error.message : 'खोज में त्रुटि हुई');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    performSearch(searchTerm);
  };

  // Perform search on component mount if there's a query parameter
  useEffect(() => {
    const query = searchParams.get('q');
    if (query) {
      setSearchTerm(query);
      performSearch(query);
    }
  }, [searchParams]);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  return (
    <Layout>
      <div className="w-full max-w-none py-4 sm:py-6 px-2 sm:px-4 lg:px-6 xl:px-8">
        <div className="max-w-4xl mx-auto">
          {/* Search Header */}
          <div className="mb-6 sm:mb-8">
            <h1 className="text-2xl sm:text-3xl font-bold mb-4">
              {t('search_results', 'खोज परिणाम')}
            </h1>
            
            {/* Search Form */}
            <Card>
              <CardContent className="pt-6">
                <form onSubmit={handleSearch} className="flex gap-2">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      type="search"
                      placeholder={t('search_placeholder', 'समाचार खोजें...')}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Button type="submit" disabled={loading}>
                    {loading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      t('search', 'खोजें')
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Search Results */}
          {loading || isTranslating ? (
            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-800">
                "{searchTerm}" के लिए खोज परिणाम लोड हो रहे हैं...
              </div>
              {Array.from({ length: 5 }).map((_, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 rounded-lg animate-pulse">
                  <div className="bg-muted rounded-md aspect-video"></div>
                  <div className="md:col-span-2 space-y-2">
                    <div className="h-4 bg-muted rounded"></div>
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="h-3 bg-muted rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : hasSearched ? (
            <ProgressiveErrorBoundary
              onError={(error) => console.error("Search results error:", error)}
              onRetry={() => performSearch(searchTerm)}
              className="mb-4"
            >
              {/* Results Count */}
              <div className="mb-4 flex items-center justify-between">
                <p className="text-sm text-muted-foreground">
                  {translatedResults.length > 0 
                    ? `${translatedResults.length} परिणाम मिले "${searchTerm}" के लिए`
                    : `"${searchTerm}" के लिए कोई परिणाम नहीं मिला`
                  }
                </p>
                {searchTerm && (
                  <Badge variant="outline" className="text-xs">
                    {searchTerm}
                  </Badge>
                )}
              </div>

              {/* Error State with improved handling */}
              {searchError && (
                <AdaptiveErrorState
                  error={searchError}
                  errorType={mapErrorType(detectErrorType(searchError))}
                  onRetry={() => performSearch(searchTerm)}
                  className="mb-4"
                  compact={true}
                />
              )}
              
              {/* Results List with improved data handling */}
              <DataLoader
                data={translatedResults}
                isLoading={false}
                error={searchError}
                onRetry={() => performSearch(searchTerm)}
                contentType="search"
                emptyMessage={`"${searchTerm}" के लिए कोई परिणाम नहीं मिला। कृपया अन्य कीवर्ड के साथ खोजें।`}
                emptyComponent={
                  <AdaptiveEmptyState
                    type="search"
                    title={`"${searchTerm}" के लिए कोई परिणाम नहीं मिला`}
                    action={{ label: "खोज साफ़ करें", onClick: () => {
                      setSearchTerm('');
                      setSearchParams({});
                    }}}
                    secondaryAction={{ label: "होमपेज पर जाएं", href: "/" }}
                  />
                }
              >
                {(articles) => (
                  <div className="space-y-4 sm:space-y-6">
                    {articles.map((article) => (
                      <NewsCardHorizontal
                        key={article.id}
                        id={article.id}
                        title={article.title}
                        excerpt={article.excerpt}
                        category={article.category}
                        imageUrl={article.imageUrl}
                        videoUrl={article.videoUrl}
                        publishedAt={article.publishedAt}
                        createdAt={article.createdAt}
                        embedVideo={false}
                      />
                    ))}
                  </div>
                )}
              </DataLoader>
            </ProgressiveErrorBoundary>
          ) : (
            <Card>
              <CardContent className="py-12 text-center">
                <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">समाचार खोजें</h3>
                <p className="text-muted-foreground mb-4">
                  ऊपर दिए गए खोज बॉक्स में अपना कीवर्ड डालें
                </p>
                <div className="text-sm text-muted-foreground">
                  <p>सुझाव:</p>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>सामान्य शब्दों का उपयोग करें</li>
                    <li>वर्तनी की जांच करें</li>
                    <li>कम शब्दों का उपयोग करें</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </Layout>
  );
}
