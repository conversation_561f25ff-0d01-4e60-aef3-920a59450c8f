import { useParams, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useMemo } from "react";
import { Layout } from "@/components/layout/layout";
import { NewsCardHorizontal } from "@/components/news/news-card-horizontal";
import { AdContainer } from "@/components/ads/AdContainer";
import { usePagination } from "@/hooks/usePagination";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis
} from "@/components/ui/pagination";
import { useNews, useLatestNewsByDate } from "@/hooks/useFirebaseData";
import { getCategoryDisplayName, getCategoryFromUrl } from "@/utils/categoryMappings";
import { useTranslatedArticles } from "@/hooks/useTranslatedContent";
import { useScrollToTopOnRouteChange } from "@/hooks/useScrollToTop";
import { detectErrorType } from "@/utils/errorUtils";
import { AdaptiveEmptyState, AdaptiveErrorState } from "@/components/ui/adaptive-empty-states";
import { DataLoader } from "@/components/ui/data-loader";
import { ProgressiveErrorBoundary } from "@/components/ui/progressive-error-boundary";

import { getVideoSettings } from "@/utils/videoSettings";

// Helper function to map error types
function mapErrorType(errorType: 'network' | 'server' | 'timeout' | 'general'): 'network' | 'server' | 'timeout' | 'permission' | 'notFound' | 'validation' | 'general' {
  switch (errorType) {
    case 'network':
      return 'network';
    case 'server':
      return 'server';
    case 'timeout':
      return 'timeout';
    default:
      return 'general';
  }
}

export default function CategoryPage() {
  const { t, i18n } = useTranslation();
  const { category } = useParams<{ category: string }>();
  const location = useLocation();
  const currentLanguage = i18n.language as 'hi' | 'en';

  // Scroll to top when route changes
  useScrollToTopOnRouteChange();

  // Memoize category calculations to prevent blinking/flickering
  const { actualCategory, categoryName, firestoreCategory } = useMemo(() => {
    // Get the category from URL - either from params or from pathname
    const getCategoryFromPath = () => {
      if (category) {
        // If we have a category param (from /category/:category route)
        return getCategoryFromUrl(category);
      } else {
        // If we're on a direct route like /national, /politics, etc.
        const pathname = location.pathname.replace('/', ''); // Remove leading slash
        return getCategoryFromUrl(pathname);
      }
    };

    const actualCat = getCategoryFromPath();
    const catName = getCategoryDisplayName(actualCat, currentLanguage);
    const firestoreCat = actualCat === 'latest' ? '' : actualCat;

    return {
      actualCategory: actualCat,
      categoryName: catName,
      firestoreCategory: firestoreCat
    };
  }, [category, location.pathname, currentLanguage]);

  // Fetch news from Firestore - use date-based fetching for latest category
  const isLatestCategory = actualCategory === 'latest';
  const { news, loading, error } = useNews(
    firestoreCategory || undefined,
    'published'
  );
  
  // For latest category, use date-based news fetching (today first, then yesterday)
  const { latestNews, loading: latestLoading, error: latestError } = useLatestNewsByDate(
    20, // Get 20 articles for pagination
    undefined, // No category filter for latest
    isLatestCategory // Only enabled for latest category
  );

  // Use appropriate data source based on category
  const finalNews = isLatestCategory ? { items: latestNews, currentPage: 1, totalPages: 1, totalItems: latestNews.length, itemsPerPage: 20 } : news;
  const finalLoading = isLatestCategory ? latestLoading : loading;
  const finalError = isLatestCategory ? latestError : error;

  // If no news found for specific category, try fetching all news as fallback
  const { news: fallbackNews, loading: fallbackLoading } = useNews(
    undefined, // No category filter
    'published'
  );

  // Use fallback if main category has no results
  const shouldUseFallback = !finalLoading && !finalError && finalNews.items.length === 0 && !isLatestCategory;
  const displayNews = shouldUseFallback ? fallbackNews : finalNews;
  const displayLoading = shouldUseFallback ? fallbackLoading : finalLoading;

  // Progressive translation - defer translation to not block initial rendering
  const { translatedArticles, isTranslating } = useTranslatedArticles(
    displayNews.items || [], 
    true, 
    'secondary', // Category pages are secondary content
    300 // 300ms delay to ensure page renders quickly
  );

  console.log('🏠 CategoryPage Debug:', {
    pathname: location.pathname,
    urlCategory: category,
    actualCategory,
    displayName: categoryName,
    firestoreCategory,
    newsCount: displayNews.items.length,
    loading: displayLoading,
    error: finalError,
    shouldUseFallback,
    allNewsItems: displayNews.items.map(item => ({ id: item.id, title: item.title, category: item.category }))
  });



  // Get video settings for embedding
  const videoSettings = getVideoSettings();

  // Use pagination hook with 20 items per page - use translated articles
  const {
    currentPage,
    totalPages,
    currentItems,
    goToPage,
    canGoNext,
    canGoPrev,
    startIndex,
    endIndex,
    totalItems
  } = usePagination({
    data: translatedArticles,
    itemsPerPage: 20
  });

  // Show enhanced loading state with content-aware skeleton UI
  if (displayLoading || isTranslating) {
    return (
      <Layout>
        <div className="container py-4 sm:py-6 px-4 sm:px-6">
          {/* Header Advertisement placeholder */}
          <div className="mb-4 sm:mb-6">
            <div className="w-full h-20 bg-muted/30 rounded-lg"></div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
            <div className="lg:col-span-3">
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-800">
                  {categoryName} समाचार लोड हो रहे हैं...
                </div>
                {Array.from({ length: 5 }).map((_, index) => (
                  <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 rounded-lg animate-pulse">
                    <div className="bg-muted rounded-md aspect-video"></div>
                    <div className="md:col-span-2 space-y-2">
                      <div className="h-4 bg-muted rounded"></div>
                      <div className="h-4 bg-muted rounded w-3/4"></div>
                      <div className="h-3 bg-muted rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="lg:col-span-1">
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="bg-muted rounded-lg h-20 animate-pulse"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  // Show adaptive error state with appropriate error type
  if (finalError) {
    const errorType = detectErrorType(finalError);
    const mappedErrorType = mapErrorType(errorType);

    return (
      <Layout>
        <div className="w-full max-w-none py-4 sm:py-6 px-2 sm:px-4 lg:px-6 xl:px-8">
          <AdaptiveErrorState
            error={finalError}
            errorType={mappedErrorType}
            onRetry={() => window.location.href = window.location.href}
          />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container py-4 sm:py-6 px-4 sm:px-6">
        {/* Header Advertisement */}
        <div className="mb-4 sm:mb-6">
          <AdContainer position="header" category={categoryName} className="text-center" />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 space-y-2 sm:space-y-0">
              <h1 className="text-2xl sm:text-3xl font-bold">{categoryName}</h1>
              {totalItems > 0 && (
                <p className="text-xs sm:text-sm text-muted-foreground">
                  Showing {startIndex + 1}-{endIndex} of {totalItems} articles
                </p>
              )}
            </div>



            <DataLoader
              data={displayNews.items}
              isLoading={false}
              error={null}
              onRetry={() => window.location.href = window.location.href}
              contentType="category"
              emptyComponent={
                <AdaptiveEmptyState
                  type="category"
                  title={shouldUseFallback ? `${categoryName} समाचार जल्द आएंगे` : `${categoryName} में कोई समाचार नहीं`}
                  description={shouldUseFallback ? 
                    `${categoryName} श्रेणी के समाचार जल्द ही उपलब्ध होंगे। अभी के लिए अन्य समाचार देखें।` :
                    `इस श्रेणी में अभी कोई समाचार उपलब्ध नहीं है। हम जल्द ही नए ${categoryName} समाचार अपडेट करेंगे।`
                  }
                  action={{ label: "सभी समाचार देखें", href: "/latest" }}
                  secondaryAction={{ label: "होमपेज पर जाएं", href: "/" }}
                />
              }
            >
              {() => (
                <ProgressiveErrorBoundary
                  onError={(error) => console.error(`Category page error: ${error}`)}
                  onRetry={() => window.location.href = window.location.href}
                  className="mb-4"
                >
                  <>
                    <div className="grid grid-cols-1 gap-4 sm:gap-6">
                      {currentItems.map((article, index) => (
                        <div key={article.id}>
                          <NewsCardHorizontal
                            id={article.id}
                            title={article.title}
                            excerpt={article.excerpt || ""}
                            category={article.category || categoryName}
                            imageUrl={article.imageUrl}
                            videoUrl={article.videoUrl}
                            publishedAt={article.publishedAt}
                            createdAt={article.createdAt}
                            embedVideo={false}
                          />
                          {/* Insert content ad after every 3rd article */}
                          {(index + 1) % 3 === 0 && (
                            <div className="my-4 sm:my-6">
                              <AdContainer position="content" category={firestoreCategory} />
                            </div>
                          )}
                        </div>
                      ))}
                    </div>

                    {/* Pagination */}
                    {totalPages > 1 && (
                      <div className="mt-8">
                        <Pagination>
                          <PaginationContent>
                            <PaginationItem>
                              <PaginationPrevious
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  if (canGoPrev) goToPage(currentPage - 1);
                                }}
                                className={!canGoPrev ? 'pointer-events-none opacity-50' : ''}
                              />
                            </PaginationItem>

                            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => {
                              // Show first page, last page, current page, and pages around current page
                              if (
                                page === 1 ||
                                page === totalPages ||
                                (page >= currentPage - 1 && page <= currentPage + 1)
                              ) {
                                return (
                                  <PaginationItem key={page}>
                                    <PaginationLink
                                      href="#"
                                      onClick={(e) => {
                                        e.preventDefault();
                                        goToPage(page);
                                      }}
                                      isActive={currentPage === page}
                                    >
                                      {page}
                                    </PaginationLink>
                                  </PaginationItem>
                                );
                              } else if (
                                page === currentPage - 2 ||
                                page === currentPage + 2
                              ) {
                                return (
                                  <PaginationItem key={page}>
                                    <PaginationEllipsis />
                                  </PaginationItem>
                                );
                              }
                              return null;
                            })}

                            <PaginationItem>
                              <PaginationNext
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  if (canGoNext) goToPage(currentPage + 1);
                                }}
                                className={!canGoNext ? 'pointer-events-none opacity-50' : ''}
                              />
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    )}
                  </>
                </ProgressiveErrorBoundary>
              )}
            </DataLoader>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 mt-6 lg:mt-0">
            <div className="sticky top-6 space-y-4 sm:space-y-6">
              <AdContainer position="sidebar" category={firestoreCategory} maxAds={3} />
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}