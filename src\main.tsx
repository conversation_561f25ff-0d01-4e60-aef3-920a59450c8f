import { createRoot } from 'react-dom/client';
import { StrictMode } from 'react';
import App from './App.tsx';
import './index.css';

// Use StrictMode only in development to catch potential issues
const AppWrapper = () => {
  if (import.meta.env.DEV) {
    return (
      <StrictMode>
        <App />
      </StrictMode>
    );
  }
  return <App />;
};

createRoot(document.getElementById('root')!).render(<AppWrapper />);
