import React, { useState } from 'react';

export const SimpleVideoTest: React.FC = () => {
  const [showVideo, setShowVideo] = useState(false);

  if (showVideo) {
    return (
      <div className="w-full bg-red-500 p-4 rounded-lg" style={{ minHeight: '400px' }}>
        <button 
          onClick={() => setShowVideo(false)}
          className="mb-4 px-4 py-2 bg-white text-black rounded"
        >
          Close Video
        </button>
        <div 
          className="w-full bg-black"
          style={{ 
            height: '350px',
            position: 'relative'
          }}
        >
          <iframe
            src="https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1"
            title="Test Video"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              border: 'none'
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-blue-500 p-4 rounded-lg" style={{ minHeight: '400px' }}>
      <h3 className="text-white text-xl mb-4">Simple Video Test</h3>
      <button 
        onClick={() => setShowVideo(true)}
        className="px-6 py-3 bg-red-600 text-white rounded-lg text-lg"
      >
        Play Test Video
      </button>
    </div>
  );
};