import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Play, X } from 'lucide-react';
import { ImageWithSkeleton } from '@/components/ui/image-skeleton';

interface YouTubeEmbedProps {
  videoUrl: string;
  thumbnailUrl?: string;
  title: string;
  aspectRatio?: 'square' | 'video' | 'portrait' | 'landscape';
  width?: number;
  height?: number;
  className?: string;
  autoPlay?: boolean;
}

export const YouTubeEmbed: React.FC<YouTubeEmbedProps> = ({
  videoUrl,
  thumbnailUrl,
  title,
  aspectRatio = 'video',
  width = 800,
  height = 450,
  className = '',
  autoPlay = false
}) => {
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [isLoading, setIsLoading] = useState(false);
  const [iframeError, setIframeError] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Function to extract YouTube video ID
  const getYouTubeVideoId = (url: string): string | null => {
    if (!url) return null;
    
    // Handle different YouTube URL formats
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^#&?]*)/,
      /youtube\.com\/v\/([^#&?]*)/,
      /youtube\.com\/user\/[^\/]*#[^\/]*\/[^\/]*\/[^\/]*\/([^#&?]*)/,
      /youtube\.com\/.*[?&]v=([^#&?]*)/
    ];
    
    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match && match[1].length === 11) {
        return match[1];
      }
    }
    
    return null;
  };

  const videoId = getYouTubeVideoId(videoUrl);
  
  if (!videoId) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`}>
        <p className="text-gray-500">Invalid YouTube URL</p>
      </div>
    );
  }

  // Generate thumbnail URL if not provided
  const defaultThumbnail = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
  const thumbnail = thumbnailUrl || defaultThumbnail;

  const handlePlayClick = useCallback(() => {
    console.log('🎬 Play button clicked');
    setIsLoading(true);
    setIsPlaying(true);
    setIframeError(false);
  }, []);

  const handleCloseVideo = useCallback(() => {
    console.log('❌ Close button clicked');
    setIsPlaying(false);
    setIsLoading(false);
    setIframeError(false);
  }, []);

  const handleIframeLoad = useCallback(() => {
    console.log('✅ Iframe loaded successfully');
    setIsLoading(false);
  }, []);

  const handleIframeError = useCallback(() => {
    console.error('❌ Iframe failed to load');
    setIsLoading(false);
    setIframeError(true);
  }, []);

  // Debug logging
  useEffect(() => {
    console.log('🎥 YouTubeEmbed state:', { isPlaying, isLoading, iframeError, videoId });
  }, [isPlaying, isLoading, iframeError, videoId]);

  const getAspectRatioClass = () => {
    switch (aspectRatio) {
      case 'square':
        return 'aspect-square';
      case 'video':
        return 'aspect-video';
      case 'portrait':
        return 'aspect-[3/4]';
      case 'landscape':
        return 'aspect-[4/3]';
      default:
        return 'aspect-video';
    }
  };

  if (isPlaying) {
    return (
      <div className={`relative ${getAspectRatioClass()} bg-black rounded-lg overflow-hidden ${className}`}>
        {/* Close button */}
        <button
          onClick={handleCloseVideo}
          className="absolute top-2 right-2 z-20 w-10 h-10 bg-black bg-opacity-70 hover:bg-opacity-90 rounded-full flex items-center justify-center transition-all duration-200 border-2 border-white border-opacity-20"
          aria-label="Close video"
        >
          <X className="w-5 h-5 text-white" />
        </button>
        
        {/* Error state */}
        {iframeError ? (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-900 text-white p-4">
            <p className="text-lg mb-2">Video failed to load</p>
            <button
              onClick={() => window.open(videoUrl, '_blank')}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
            >
              Open in YouTube
            </button>
          </div>
        ) : (
          <>
            {/* YouTube iframe */}
            <iframe
              ref={iframeRef}
              src={`https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0&modestbranding=1&enablejsapi=1`}
              title={title}
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen
              className="absolute inset-0 w-full h-full z-10"
              onLoad={handleIframeLoad}
              onError={handleIframeError}
              style={{ 
                border: 'none',
                outline: 'none',
                minHeight: '100%',
                minWidth: '100%',
                display: 'block',
                visibility: 'visible'
              }}
            />
            
            {/* Loading overlay */}
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75 z-10">
                <div className="flex flex-col items-center text-white">
                  <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin mb-2"></div>
                  <p className="text-sm">Loading video...</p>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    );
  }

  return (
    <div className={`relative ${getAspectRatioClass()} ${className}`}>
      <ImageWithSkeleton
        src={thumbnail}
        alt={title}
        aspectRatio={aspectRatio}
        width={width}
        height={height}
        className="w-full h-full object-cover rounded-lg"
      />
      
      {/* Play button overlay */}
      <button
        onClick={handlePlayClick}
        className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 hover:bg-opacity-40 transition-opacity rounded-lg group"
        aria-label="Play video"
      >
        <div className="w-16 h-16 sm:w-20 sm:h-20 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center transition-colors group-hover:scale-110 transform duration-200">
          <Play className="w-8 h-8 sm:w-10 sm:h-10 text-white ml-1" fill="white" />
        </div>
      </button>
      
      {/* Video duration badge (optional) */}
      <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
        Video
      </div>
    </div>
  );
};