import React from "react";
import { Link } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MediaDisplayCompact } from "@/components/ui/media-display";
import { scrollToTop } from "@/utils/scrollUtils";
import { formatRelativeDate } from "@/utils/dateFormatter";

interface NewsCardHorizontalProps {
  id: string;
  title: string;
  excerpt?: string;
  category: string;
  imageUrl: string;
  videoUrl?: string;
  publishedAt: any; // Can be string, Date, or Firestore Timestamp
  createdAt?: any; // Fallback date
  embedVideo?: boolean; // Option to embed videos in cards
}

const NewsCardHorizontal = React.memo(function NewsCardHorizontal({
  id,
  title,
  excerpt,
  category,
  imageUrl,
  videoUrl,
  publishedAt,
  createdAt,
  embedVideo = false, // Default to external link
}: NewsCardHorizontalProps) {
  const handleClick = React.useCallback(() => {
    scrollToTop();
  }, []);

  return (
    <Link to={`/news/${id}`} className="block" onClick={handleClick}>
      <Card className="overflow-hidden hover:shadow-md transition-shadow duration-300">
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
          <div className="relative sm:col-span-1">
            <MediaDisplayCompact
              imageUrl={imageUrl}
              videoUrl={videoUrl}
              alt={title}
              aspectRatio="landscape"
              category={category}
              width={400}
              height={300}
              className="w-full h-48 sm:h-full"
              embedVideo={embedVideo}
            />
            <Badge className="absolute top-2 left-2 text-xs">
              {category}
            </Badge>
          </div>
          <CardContent className="p-3 sm:p-4 sm:col-span-2 space-y-2">
            <h3 className="text-base sm:text-lg lg:text-xl font-bold line-clamp-2 sm:line-clamp-3 leading-tight">{title}</h3>
            {excerpt && (
              <p className="text-sm sm:text-base text-muted-foreground line-clamp-2 leading-relaxed">{excerpt}</p>
            )}
            <div className="text-xs sm:text-sm text-muted-foreground">
              {formatRelativeDate(publishedAt, createdAt)}
            </div>
          </CardContent>
        </div>
      </Card>
    </Link>
  );
});

export { NewsCardHorizontal };