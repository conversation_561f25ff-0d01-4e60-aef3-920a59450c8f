import React from 'react';

import { AdaptiveEmptyState, AdaptiveErrorState, PartialContentErrorState } from '@/components/ui/adaptive-empty-states';
import { detectErrorType } from '@/utils/errorUtils';

/**
 * Component that handles data loading, empty states, and error states in a consistent way
 * Provides better user experience with appropriate loading, empty, and error states
 */
export const DataLoader = <T,>({
  data,
  isLoading,
  error,
  onRetry,
  loadingComponent,
  emptyComponent,
  errorComponent,
  contentType = 'generic',
  loadingMessage,
  emptyMessage,
  errorMessage,
  isCritical = false,
  compact = false,
  className = '',
  children
}: {
  data: T | null | undefined;
  isLoading: boolean;
  error: any;
  onRetry?: () => void;
  loadingComponent?: React.ReactNode;
  emptyComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  contentType?: 'news' | 'category' | 'search' | 'video' | 'gallery' | 'tags' | 'comments' | 'user' | 'notification' | 'filter' | 'generic';
  loadingMessage?: string;
  emptyMessage?: string;
  errorMessage?: string;
  isCritical?: boolean;
  compact?: boolean;
  className?: string;
  children: (data: T) => React.ReactNode;
}) => {
  // Show custom or default loading state
  if (isLoading) {
    if (loadingComponent) {
      return <>{loadingComponent}</>;
    }
    
    return (
      <div className={`bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-800 ${className}`}>
        <div className="flex items-center">
          <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
          <span>{loadingMessage || getDefaultLoadingMessage(contentType)}</span>
        </div>
      </div>
    );
  }
  
  // Show custom or default error state
  if (error) {
    if (errorComponent) {
      return <>{errorComponent}</>;
    }
    
    const errorType = detectErrorType(error);
    const mappedErrorType = mapErrorType(errorType);
    
    if (isCritical) {
      return (
        <AdaptiveErrorState
          error={errorMessage || error.toString()}
          errorType={mappedErrorType}
          onRetry={onRetry}
          className={className}
          compact={compact}
        />
      );
    }
    
    return (
      <PartialContentErrorState
        message={errorMessage || error.toString()}
        onRetry={onRetry}
        className={className}
        severity={getSeverityFromErrorType(errorType)}
      />
    );
  }
  
  // Show custom or default empty state
  if (!data || (Array.isArray(data) && data.length === 0)) {
    if (emptyComponent) {
      return <>{emptyComponent}</>;
    }
    
    return (
      <AdaptiveEmptyState
        type={contentType}
        description={emptyMessage || getDefaultEmptyMessage(contentType)}
        action={onRetry ? { label: "पुनः प्रयास करें", onClick: onRetry } : undefined}
        className={className}
        compact={compact}
      />
    );
  }
  
  // Show content
  return <>{children(data)}</>;
};

/**
 * Component that handles data loading with a retry mechanism
 */
export const RetryableDataLoader = <T,>({
  loadData,
  renderContent,
  loadingComponent,
  emptyComponent,
  errorComponent,
  contentType = 'generic',
  loadingMessage,
  emptyMessage,
  errorMessage,
  isCritical = false,
  compact = false,
  className = '',
  maxRetries = 3
}: {
  loadData: () => Promise<T>;
  renderContent: (data: T) => React.ReactNode;
  loadingComponent?: React.ReactNode;
  emptyComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  contentType?: 'news' | 'category' | 'search' | 'video' | 'gallery' | 'tags' | 'comments' | 'user' | 'notification' | 'filter' | 'generic';
  loadingMessage?: string;
  emptyMessage?: string;
  errorMessage?: string;
  isCritical?: boolean;
  compact?: boolean;
  className?: string;
  maxRetries?: number;
}) => {
  const [data, setData] = React.useState<T | null>(null);
  const [isLoading, setIsLoading] = React.useState(true);
  const [error, setError] = React.useState<any>(null);
  const [retryCount, setRetryCount] = React.useState(0);
  
  const fetchData = React.useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await loadData();
      setData(result);
    } catch (err) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  }, [loadData]);
  
  React.useEffect(() => {
    fetchData();
  }, [fetchData]);
  
  const handleRetry = React.useCallback(() => {
    if (retryCount < maxRetries) {
      setRetryCount(prev => prev + 1);
      fetchData();
    }
  }, [retryCount, maxRetries, fetchData]);
  
  return (
    <DataLoader
      data={data}
      isLoading={isLoading}
      error={error}
      onRetry={handleRetry}
      loadingComponent={loadingComponent}
      emptyComponent={emptyComponent}
      errorComponent={errorComponent}
      contentType={contentType}
      loadingMessage={loadingMessage}
      emptyMessage={emptyMessage}
      errorMessage={errorMessage}
      isCritical={isCritical}
      compact={compact}
      className={className}
    >
      {renderContent}
    </DataLoader>
  );
};

// Helper functions
function mapContentType(contentType: string): 'article' | 'card' | 'list' | 'grid' | 'table' | 'comment' | 'form' | 'search' {
  switch (contentType) {
    case 'news':
      return 'article';
    case 'category':
      return 'list';
    case 'search':
      return 'search';
    case 'video':
      return 'grid';
    case 'gallery':
      return 'grid';
    case 'tags':
      return 'list';
    case 'comments':
      return 'comment';
    case 'user':
      return 'card';
    case 'notification':
      return 'list';
    case 'filter':
      return 'list';
    default:
      return 'card';
  }
}

function mapErrorType(errorType: 'network' | 'server' | 'timeout' | 'general'): 'network' | 'server' | 'timeout' | 'permission' | 'notFound' | 'validation' | 'general' {
  switch (errorType) {
    case 'network':
      return 'network';
    case 'server':
      return 'server';
    case 'timeout':
      return 'timeout';
    default:
      return 'general';
  }
}

function getSeverityFromErrorType(errorType: 'network' | 'server' | 'timeout' | 'general'): 'low' | 'medium' | 'high' {
  switch (errorType) {
    case 'network':
      return 'medium';
    case 'server':
      return 'high';
    case 'timeout':
      return 'medium';
    default:
      return 'low';
  }
}

function getDefaultLoadingMessage(contentType: string): string {
  switch (contentType) {
    case 'news':
      return 'समाचार लोड हो रहे हैं...';
    case 'category':
      return 'श्रेणी सामग्री लोड हो रही है...';
    case 'search':
      return 'खोज परिणाम लोड हो रहे हैं...';
    case 'video':
      return 'वीडियो लोड हो रहे हैं...';
    case 'gallery':
      return 'छवियां लोड हो रही हैं...';
    case 'tags':
      return 'टैग लोड हो रहे हैं...';
    case 'comments':
      return 'टिप्पणियां लोड हो रही हैं...';
    case 'user':
      return 'उपयोगकर्ता जानकारी लोड हो रही है...';
    case 'notification':
      return 'सूचनाएं लोड हो रही हैं...';
    case 'filter':
      return 'फ़िल्टर परिणाम लोड हो रहे हैं...';
    default:
      return 'लोड हो रहा है...';
  }
}

function getDefaultEmptyMessage(contentType: string): string {
  switch (contentType) {
    case 'news':
      return 'इस समय कोई समाचार उपलब्ध नहीं है। हमारी टीम नए समाचार अपडेट कर रही है। कृपया बाद में पुनः प्रयास करें।';
    case 'category':
      return 'इस श्रेणी में अभी कोई समाचार उपलब्ध नहीं है। कृपया अन्य श्रेणियां देखें।';
    case 'search':
      return 'आपकी खोज के लिए कोई परिणाम नहीं मिला। कृपया अन्य कीवर्ड के साथ खोजें या अपनी खोज को अधिक सामान्य बनाएं।';
    case 'video':
      return 'इस समय कोई वीडियो उपलब्ध नहीं है। हम जल्द ही नए वीडियो अपलोड करेंगे।';
    case 'gallery':
      return 'इस समय फोटो गैलरी में कोई छवि उपलब्ध नहीं है। हम जल्द ही नई तस्वीरें अपलोड करेंगे।';
    case 'tags':
      return 'इस समय कोई ट्रेंडिंग टैग उपलब्ध नहीं है। लोकप्रियता बढ़ने पर टैग यहां दिखाई देंगे।';
    case 'comments':
      return 'इस समाचार पर अभी तक कोई टिप्पणी नहीं की गई है। पहली टिप्पणी करने वाले बनें!';
    case 'user':
      return 'आपके खोज मापदंडों से मेल खाने वाला कोई उपयोगकर्ता नहीं मिला।';
    case 'notification':
      return 'आपके पास इस समय कोई सूचना नहीं है।';
    case 'filter':
      return 'आपके फ़िल्टर मापदंडों से मेल खाने वाला कोई परिणाम नहीं मिला। कृपया अपने फ़िल्टर को समायोजित करें।';
    default:
      return 'अनुरोधित जानकारी इस समय उपलब्ध नहीं है।';
  }
}