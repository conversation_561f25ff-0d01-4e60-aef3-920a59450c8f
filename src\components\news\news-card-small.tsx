import React from "react";
import { Link } from "react-router-dom";
import { MediaDisplayCompact } from "@/components/ui/media-display";
import { scrollToTop } from "@/utils/scrollUtils";

interface NewsCardSmallProps {
  id: string;
  title: string;
  imageUrl: string;
  videoUrl?: string;
  publishedAt: string;
}

const NewsCardSmall = React.memo(function NewsCardSmall({
  id,
  title,
  imageUrl,
  videoUrl,
  publishedAt,
}: NewsCardSmallProps) {
  const handleClick = React.useCallback(() => {
    scrollToTop();
  }, []);

  return (
    <Link to={`/news/${id}`} className="group block" onClick={handleClick}>
      <div className="grid grid-cols-3 gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg hover:bg-muted/50 transition-colors duration-300">
        <div className="relative overflow-hidden rounded-md">
          <MediaDisplayCompact
            imageUrl={imageUrl}
            videoUrl={videoUrl}
            alt={title}
            aspectRatio="square"
            width={200}
            height={200}
            className="group-hover:scale-105 transition-transform duration-300"
          />
        </div>
        <div className="col-span-2 space-y-1 flex flex-col justify-between">
          <h4 className="text-sm sm:text-base font-medium line-clamp-2 sm:line-clamp-3 group-hover:text-primary transition-colors leading-tight">{title}</h4>
          <div className="text-xs text-muted-foreground">
            {publishedAt || new Date().toLocaleDateString('hi-IN')}
          </div>
        </div>
      </div>
    </Link>
  );
});

export { NewsCardSmall };