import React, { useState } from 'react';
import { YouTubeEmbed } from '@/components/media/YouTubeEmbed';
import { SimpleVideoTest } from './SimpleVideoTest';

export const VideoDebug: React.FC = () => {
  const [testVideoUrl] = useState('https://www.youtube.com/watch?v=dQw4w9WgXcQ'); // Rick <PERSON> for testing
  
  return (
    <div className="p-4 border-2 border-red-500 bg-red-50">
      <h3 className="text-lg font-bold mb-4 text-red-800">Video Debug Component</h3>
      <div className="space-y-4">
        <p className="text-sm text-red-700">
          <strong>Test Video URL:</strong> {testVideoUrl}
        </p>
        
        <div className="bg-white p-4 rounded border">
          <h4 className="font-semibold mb-2">Direct YouTubeEmbed Test:</h4>
          <div className="w-full max-w-2xl" style={{ minHeight: '300px' }}>
            <YouTubeEmbed
              videoUrl={testVideoUrl}
              title="Test Video"
              aspectRatio="video"
              className="border-2 border-blue-500"
            />
          </div>
        </div>
        
        <div className="bg-white p-4 rounded border">
          <h4 className="font-semibold mb-2">Simple iframe test:</h4>
          <div 
            className="w-full max-w-2xl bg-gray-200 relative border-2 border-green-500"
            style={{ 
              aspectRatio: '16/9',
              minHeight: '300px'
            }}
          >
            <iframe
              src="https://www.youtube.com/embed/dQw4w9WgXcQ"
              title="Test iframe"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              className="w-full h-full"
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                minHeight: '300px'
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};