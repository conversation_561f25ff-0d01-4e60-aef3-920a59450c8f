import React from 'react';
import { Button } from '@/components/ui/button';
import { 
  AlertCircle, 
  FileQuestion, 
  RefreshCw, 
  Search, 
  Newspaper, 
  Video, 
  Image, 
  Tag,
  Info,
  WifiOff,
  Clock,
  Server,
  Inbox,
  Filter,
  MessageSquare,
  User,
  ShieldAlert,
  HelpCircle
} from 'lucide-react';
import { Link } from 'react-router-dom';

/**
 * Component that shows an empty state with contextual information based on the content type
 * Provides better user experience by explaining why content is not available
 */
export const AdaptiveEmptyState: React.FC<{
  type: 'news' | 'category' | 'search' | 'video' | 'gallery' | 'tags' | 'comments' | 'user' | 'notification' | 'filter' | 'generic';
  title?: string;
  description?: string;
  action?: {
    label: string;
    onClick?: () => void;
    href?: string;
  };
  secondaryAction?: {
    label: string;
    onClick?: () => void;
    href?: string;
  };
  className?: string;
  compact?: boolean;
  icon?: React.ReactNode;
}> = ({
  type,
  title,
  description,
  action,
  secondaryAction,
  className = '',
  compact = false,
  icon
}) => {
  // Default content based on type
  const getDefaultContent = () => {
    switch (type) {
      case 'news':
        return {
          icon: icon || <Newspaper className={compact ? "h-6 w-6" : "h-8 w-8"} />,
          title: title || "कोई समाचार नहीं मिला",
          description: description || "इस समय कोई समाचार उपलब्ध नहीं है। हमारी टीम नए समाचार अपडेट कर रही है। कृपया बाद में पुनः प्रयास करें।"
        };
        
      case 'category':
        return {
          icon: icon || <FileQuestion className={compact ? "h-6 w-6" : "h-8 w-8"} />,
          title: title || "इस श्रेणी में कोई समाचार नहीं",
          description: description || "इस श्रेणी में अभी कोई समाचार उपलब्ध नहीं है। कृपया अन्य श्रेणियां देखें।"
        };
        
      case 'search':
        return {
          icon: icon || <Search className={compact ? "h-6 w-6" : "h-8 w-8"} />,
          title: title || "कोई परिणाम नहीं मिला",
          description: description || "आपकी खोज के लिए कोई परिणाम नहीं मिला। कृपया अन्य कीवर्ड के साथ खोजें या अपनी खोज को अधिक सामान्य बनाएं।"
        };
        
      case 'video':
        return {
          icon: icon || <Video className={compact ? "h-6 w-6" : "h-8 w-8"} />,
          title: title || "कोई वीडियो नहीं मिला",
          description: description || "इस समय कोई वीडियो उपलब्ध नहीं है। हम जल्द ही नए वीडियो अपलोड करेंगे।"
        };
        
      case 'gallery':
        return {
          icon: icon || <Image className={compact ? "h-6 w-6" : "h-8 w-8"} />,
          title: title || "कोई छवि नहीं मिली",
          description: description || "इस समय फोटो गैलरी में कोई छवि उपलब्ध नहीं है। हम जल्द ही नई तस्वीरें अपलोड करेंगे।"
        };
        
      case 'tags':
        return {
          icon: icon || <Tag className={compact ? "h-6 w-6" : "h-8 w-8"} />,
          title: title || "कोई ट्रेंडिंग टैग नहीं",
          description: description || "इस समय कोई ट्रेंडिंग टैग उपलब्ध नहीं है। लोकप्रियता बढ़ने पर टैग यहां दिखाई देंगे।"
        };
        
      case 'comments':
        return {
          icon: icon || <MessageSquare className={compact ? "h-6 w-6" : "h-8 w-8"} />,
          title: title || "कोई टिप्पणी नहीं",
          description: description || "इस समाचार पर अभी तक कोई टिप्पणी नहीं की गई है। पहली टिप्पणी करने वाले बनें!"
        };
        
      case 'user':
        return {
          icon: icon || <User className={compact ? "h-6 w-6" : "h-8 w-8"} />,
          title: title || "कोई उपयोगकर्ता नहीं मिला",
          description: description || "आपके खोज मापदंडों से मेल खाने वाला कोई उपयोगकर्ता नहीं मिला।"
        };
        
      case 'notification':
        return {
          icon: icon || <Inbox className={compact ? "h-6 w-6" : "h-8 w-8"} />,
          title: title || "कोई सूचना नहीं",
          description: description || "आपके पास इस समय कोई सूचना नहीं है।"
        };
        
      case 'filter':
        return {
          icon: icon || <Filter className={compact ? "h-6 w-6" : "h-8 w-8"} />,
          title: title || "कोई परिणाम नहीं",
          description: description || "आपके फ़िल्टर मापदंडों से मेल खाने वाला कोई परिणाम नहीं मिला। कृपया अपने फ़िल्टर को समायोजित करें।"
        };
        
      case 'generic':
      default:
        return {
          icon: icon || <HelpCircle className={compact ? "h-6 w-6" : "h-8 w-8"} />,
          title: title || "कोई डेटा नहीं मिला",
          description: description || "अनुरोधित जानकारी इस समय उपलब्ध नहीं है।"
        };
    }
  };
  
  const content = getDefaultContent();
  
  // Compact version for inline use
  if (compact) {
    return (
      <div className={`flex items-center p-3 bg-muted/30 border border-muted rounded-md ${className}`}>
        <div className="text-muted-foreground mr-3">
          {content.icon}
        </div>
        <div className="flex-1">
          <p className="text-sm font-medium">{content.title}</p>
          <p className="text-xs text-muted-foreground">{content.description}</p>
        </div>
        {action && (
          <div className="ml-3">
            {action.href ? (
              <Link to={action.href}>
                <Button size="sm" variant="outline">{action.label}</Button>
              </Link>
            ) : (
              <Button size="sm" variant="outline" onClick={action.onClick}>{action.label}</Button>
            )}
          </div>
        )}
      </div>
    );
  }
  
  // Full version for dedicated empty state
  return (
    <div className={`flex flex-col items-center justify-center py-8 sm:py-12 px-4 text-center ${className}`}>
      <div className="bg-muted/50 rounded-full p-4 mb-4">
        <div className="text-muted-foreground">
          {content.icon}
        </div>
      </div>
      <h3 className="text-lg font-semibold mb-2">{content.title}</h3>
      <p className="text-muted-foreground mb-6 max-w-md">{content.description}</p>
      <div className="flex flex-wrap gap-3 justify-center">
        {action && (
          action.href ? (
            <Link to={action.href}>
              <Button>{action.label}</Button>
            </Link>
          ) : (
            <Button onClick={action.onClick}>{action.label}</Button>
          )
        )}
        {secondaryAction && (
          secondaryAction.href ? (
            <Link to={secondaryAction.href}>
              <Button variant="outline">{secondaryAction.label}</Button>
            </Link>
          ) : (
            <Button variant="outline" onClick={secondaryAction.onClick}>{secondaryAction.label}</Button>
          )
        )}
      </div>
    </div>
  );
};

/**
 * Component that shows an adaptive error state based on the error type
 */
export const AdaptiveErrorState: React.FC<{
  error: string;
  errorType?: 'network' | 'server' | 'timeout' | 'permission' | 'notFound' | 'validation' | 'general';
  onRetry?: () => void;
  className?: string;
  compact?: boolean;
}> = ({
  error,
  errorType = 'general',
  onRetry,
  className = '',
  compact = false
}) => {
  // Get appropriate error icon
  const getErrorIcon = () => {
    switch (errorType) {
      case 'network':
        return <WifiOff className={compact ? "h-5 w-5" : "h-10 w-10"} />;
      case 'server':
        return <Server className={compact ? "h-5 w-5" : "h-10 w-10"} />;
      case 'timeout':
        return <Clock className={compact ? "h-5 w-5" : "h-10 w-10"} />;
      case 'permission':
        return <ShieldAlert className={compact ? "h-5 w-5" : "h-10 w-10"} />;
      case 'notFound':
        return <FileQuestion className={compact ? "h-5 w-5" : "h-10 w-10"} />;
      case 'validation':
        return <AlertCircle className={compact ? "h-5 w-5" : "h-10 w-10"} />;
      default:
        return <AlertCircle className={compact ? "h-5 w-5" : "h-10 w-10"} />;
    }
  };

  // Get appropriate error title
  const getErrorTitle = () => {
    switch (errorType) {
      case 'network':
        return "नेटवर्क त्रुटि";
      case 'server':
        return "सर्वर त्रुटि";
      case 'timeout':
        return "टाइमआउट त्रुटि";
      case 'permission':
        return "अनुमति त्रुटि";
      case 'notFound':
        return "नहीं मिला";
      case 'validation':
        return "मान्यता त्रुटि";
      default:
        return "त्रुटि";
    }
  };
  
  // Compact version for inline use
  if (compact) {
    return (
      <div className={`flex items-center p-3 bg-red-50 border border-red-100 rounded-md ${className}`}>
        <div className="text-red-500 mr-3">
          {getErrorIcon()}
        </div>
        <div className="flex-1">
          <p className="text-sm font-medium text-red-800">{getErrorTitle()}</p>
          <p className="text-xs text-red-600">{error}</p>
        </div>
        {onRetry && (
          <button 
            onClick={onRetry}
            className="ml-3 text-xs flex items-center text-red-600 hover:text-red-800"
          >
            <RefreshCw className="mr-1 h-3 w-3" />
            पुनः प्रयास करें
          </button>
        )}
      </div>
    );
  }
  
  // Full version for dedicated error state
  return (
    <div className={`flex flex-col items-center justify-center py-8 sm:py-12 px-4 text-center ${className}`}>
      <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
        <div className="text-red-500 mb-4">
          {getErrorIcon()}
        </div>
        <h3 className="text-lg font-semibold text-red-800 mb-2">{getErrorTitle()}</h3>
        <p className="text-red-600 text-sm mb-4">{error}</p>
        <div className="flex flex-wrap gap-3 justify-center">
          {onRetry && (
            <Button 
              onClick={onRetry}
              variant="outline"
              className="border-red-300 hover:bg-red-50 text-red-600"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              पुनः प्रयास करें
            </Button>
          )}
          <Button 
            variant="outline"
            className="border-red-300 hover:bg-red-50 text-red-600"
            onClick={() => window.location.href = '/'}
          >
            होमपेज पर जाएं
          </Button>
        </div>
      </div>
    </div>
  );
};

/**
 * Component that shows a partial content error that doesn't block other content
 */
export const PartialContentErrorState: React.FC<{
  message: string;
  onRetry?: () => void;
  className?: string;
  severity?: 'low' | 'medium' | 'high';
}> = ({
  message,
  onRetry,
  className = '',
  severity = 'medium'
}) => {
  // Get appropriate color based on severity
  const getSeverityColors = () => {
    switch (severity) {
      case 'low':
        return {
          bg: 'bg-blue-50',
          border: 'border-blue-100',
          text: 'text-blue-800',
          textSecondary: 'text-blue-600',
          icon: <Info className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5" />
        };
      case 'high':
        return {
          bg: 'bg-red-50',
          border: 'border-red-100',
          text: 'text-red-800',
          textSecondary: 'text-red-600',
          icon: <AlertCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
        };
      case 'medium':
      default:
        return {
          bg: 'bg-amber-50',
          border: 'border-amber-100',
          text: 'text-amber-800',
          textSecondary: 'text-amber-700',
          icon: <AlertCircle className="h-5 w-5 text-amber-500 mr-2 flex-shrink-0 mt-0.5" />
        };
    }
  };
  
  const colors = getSeverityColors();
  
  return (
    <div className={`${colors.bg} ${colors.border} border rounded-lg p-3 ${className}`}>
      <div className="flex items-start">
        {colors.icon}
        <div className="flex-1">
          <p className={`text-sm font-medium ${colors.text} mb-1`}>
            {severity === 'low' ? 'सूचना' : severity === 'high' ? 'महत्वपूर्ण त्रुटि' : 'आंशिक डेटा लोड हुआ'}
          </p>
          <p className={`text-xs ${colors.textSecondary}`}>{message}</p>
        </div>
        {onRetry && (
          <button 
            onClick={onRetry}
            className={`text-xs flex items-center ${colors.textSecondary} hover:underline ml-2`}
          >
            <RefreshCw className="mr-1 h-3 w-3" />
            पुनः प्रयास करें
          </button>
        )}
      </div>
    </div>
  );
};