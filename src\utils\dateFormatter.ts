// Utility functions for consistent date formatting across the application

export const formatPublishedDate = (publishedAt: any, createdAt?: any): string => {
  try {
    // If publishedAt is already a formatted string, return it
    if (typeof publishedAt === 'string' && publishedAt.includes('/')) {
      return publishedAt;
    }
    
    // If publishedAt is a Date object
    if (publishedAt instanceof Date) {
      return publishedAt.toLocaleDateString('hi-IN');
    }
    
    // If publishedAt is a Firestore Timestamp
    if (publishedAt && typeof publishedAt.toDate === 'function') {
      return publishedAt.toDate().toLocaleDateString('hi-IN');
    }
    
    // Fallback to createdAt if publishedAt is not available
    if (createdAt) {
      if (createdAt instanceof Date) {
        return createdAt.toLocaleDateString('hi-IN');
      }
      if (typeof createdAt.toDate === 'function') {
        return createdAt.toDate().toLocaleDateString('hi-IN');
      }
    }
    
    // Final fallback to current date
    return new Date().toLocaleDateString('hi-IN');
  } catch (error) {
    console.error('Error formatting date:', error);
    return new Date().toLocaleDateString('hi-IN');
  }
};

export const formatRelativeDate = (publishedAt: any, createdAt?: any): string => {
  try {
    // If publishedAt is already a formatted Hindi string, return it as is
    if (typeof publishedAt === 'string' && 
        (publishedAt.includes('पहले') || publishedAt.includes('अभी') || publishedAt.includes('कल'))) {
      return publishedAt;
    }
    
    let date: Date;
    
    // Convert to Date object
    if (publishedAt instanceof Date) {
      date = publishedAt;
    } else if (publishedAt && typeof publishedAt.toDate === 'function') {
      date = publishedAt.toDate();
    } else if (createdAt instanceof Date) {
      date = createdAt;
    } else if (createdAt && typeof createdAt.toDate === 'function') {
      date = createdAt.toDate();
    } else {
      // If we can't parse the date, return a fallback
      return typeof publishedAt === 'string' ? publishedAt : 'अभी';
    }
    
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);
    
    if (diffInMinutes < 1) {
      return 'अभी';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} मिनट पहले`;
    } else if (diffInHours < 24) {
      return `${diffInHours} घंटे पहले`;
    } else if (diffInDays === 1) {
      return 'कल';
    } else if (diffInDays < 7) {
      return `${diffInDays} दिन पहले`;
    } else {
      return date.toLocaleDateString('hi-IN');
    }
  } catch (error) {
    console.error('Error formatting relative date:', error);
    return typeof publishedAt === 'string' ? publishedAt : 'अभी';
  }
};