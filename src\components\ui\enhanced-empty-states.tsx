import React from 'react';
import { Button } from '@/components/ui/button';
import { 
  AlertCircle, 
  FileQuestion, 
  RefreshCw, 
  Search, 
  Newspaper, 
  Video, 
  Image, 
  Tag,
  Info,
  WifiOff,
  Clock,
  Server
} from 'lucide-react';
import { Link } from 'react-router-dom';

interface EnhancedEmptyStateProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  action?: {
    label: string;
    onClick?: () => void;
    href?: string;
  };
  secondaryAction?: {
    label: string;
    onClick?: () => void;
    href?: string;
  };
  className?: string;
}

export const EnhancedEmptyState: React.FC<EnhancedEmptyStateProps> = ({
  title,
  description,
  icon,
  action,
  secondaryAction,
  className = ''
}) => {
  return (
    <div className={`flex flex-col items-center justify-center py-8 sm:py-12 px-4 text-center ${className}`}>
      <div className="bg-muted/50 rounded-full p-4 mb-4">
        {icon || <FileQuestion className="h-8 w-8 text-muted-foreground" />}
      </div>
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground mb-6 max-w-md">{description}</p>
      <div className="flex flex-wrap gap-3 justify-center">
        {action && (
          action.href ? (
            <Link to={action.href}>
              <Button>{action.label}</Button>
            </Link>
          ) : (
            <Button onClick={action.onClick}>{action.label}</Button>
          )
        )}
        {secondaryAction && (
          secondaryAction.href ? (
            <Link to={secondaryAction.href}>
              <Button variant="outline">{secondaryAction.label}</Button>
            </Link>
          ) : (
            <Button variant="outline" onClick={secondaryAction.onClick}>{secondaryAction.label}</Button>
          )
        )}
      </div>
    </div>
  );
};

// Enhanced empty states for different content types
export const EnhancedNewsEmptyState: React.FC<{ 
  onRetry?: () => void;
  category?: string;
}> = ({ onRetry, category }) => (
  <EnhancedEmptyState
    title={category ? `${category} में कोई समाचार नहीं मिला` : "कोई समाचार नहीं मिला"}
    description="इस समय कोई समाचार उपलब्ध नहीं है। हमारी टीम नए समाचार अपडेट कर रही है। कृपया बाद में पुनः प्रयास करें।"
    icon={<Newspaper className="h-8 w-8 text-muted-foreground" />}
    action={onRetry ? { label: "पुनः प्रयास करें", onClick: onRetry } : { label: "होमपेज पर जाएं", href: "/" }}
    secondaryAction={category ? { label: "सभी समाचार देखें", href: "/latest" } : undefined}
  />
);

export const EnhancedCategoryEmptyState: React.FC<{ 
  category: string;
  onRetry?: () => void;
}> = ({ category, onRetry }) => (
  <EnhancedEmptyState
    title={`${category} में कोई समाचार नहीं`}
    description={`इस श्रेणी में अभी कोई समाचार उपलब्ध नहीं है। हम जल्द ही नए ${category} समाचार अपडेट करेंगे। कृपया अन्य श्रेणियां देखें।`}
    icon={<FileQuestion className="h-8 w-8 text-muted-foreground" />}
    action={{ label: "सभी समाचार देखें", href: "/latest" }}
    secondaryAction={onRetry ? { label: "पुनः प्रयास करें", onClick: onRetry } : undefined}
  />
);

export const EnhancedSearchEmptyState: React.FC<{ 
  query: string;
  onClear?: () => void;
}> = ({ query, onClear }) => (
  <EnhancedEmptyState
    title="कोई परिणाम नहीं मिला"
    description={`"${query}" के लिए कोई परिणाम नहीं मिला। कृपया अन्य कीवर्ड के साथ खोजें या अपनी खोज को अधिक सामान्य बनाएं।`}
    icon={<Search className="h-8 w-8 text-muted-foreground" />}
    action={onClear ? { label: "खोज साफ़ करें", onClick: onClear } : undefined}
    secondaryAction={{ label: "होमपेज पर जाएं", href: "/" }}
  />
);

export const EnhancedVideosEmptyState: React.FC<{
  onRetry?: () => void;
}> = ({ onRetry }) => (
  <EnhancedEmptyState
    title="कोई वीडियो नहीं मिला"
    description="इस समय कोई वीडियो उपलब्ध नहीं है। हम जल्द ही नए वीडियो अपलोड करेंगे।"
    icon={<Video className="h-8 w-8 text-muted-foreground" />}
    action={onRetry ? { label: "पुनः प्रयास करें", onClick: onRetry } : undefined}
    secondaryAction={{ label: "होमपेज पर जाएं", href: "/" }}
  />
);

export const EnhancedGalleryEmptyState: React.FC<{
  onRetry?: () => void;
}> = ({ onRetry }) => (
  <EnhancedEmptyState
    title="कोई छवि नहीं मिली"
    description="इस समय फोटो गैलरी में कोई छवि उपलब्ध नहीं है। हम जल्द ही नई तस्वीरें अपलोड करेंगे।"
    icon={<Image className="h-8 w-8 text-muted-foreground" />}
    action={onRetry ? { label: "पुनः प्रयास करें", onClick: onRetry } : undefined}
    secondaryAction={{ label: "होमपेज पर जाएं", href: "/" }}
  />
);

export const EnhancedTagsEmptyState: React.FC<{
  onRetry?: () => void;
}> = ({ onRetry }) => (
  <EnhancedEmptyState
    title="कोई ट्रेंडिंग टैग नहीं"
    description="इस समय कोई ट्रेंडिंग टैग उपलब्ध नहीं है। लोकप्रियता बढ़ने पर टैग यहां दिखाई देंगे।"
    icon={<Tag className="h-8 w-8 text-muted-foreground" />}
    action={onRetry ? { label: "पुनः प्रयास करें", onClick: onRetry } : undefined}
  />
);

// Enhanced error states with more specific information
export const EnhancedErrorState: React.FC<{ 
  error: string;
  onRetry?: () => void;
  errorType?: 'network' | 'server' | 'timeout' | 'general';
}> = ({ error, onRetry, errorType = 'general' }) => {
  // Choose icon based on error type
  const getErrorIcon = () => {
    switch (errorType) {
      case 'network': return <WifiOff className="h-10 w-10 mx-auto" />;
      case 'server': return <Server className="h-10 w-10 mx-auto" />;
      case 'timeout': return <Clock className="h-10 w-10 mx-auto" />;
      default: return <AlertCircle className="h-10 w-10 mx-auto" />;
    }
  };

  // Get appropriate error title
  const getErrorTitle = () => {
    switch (errorType) {
      case 'network': return "नेटवर्क त्रुटि";
      case 'server': return "सर्वर त्रुटि";
      case 'timeout': return "टाइमआउट त्रुटि";
      default: return "त्रुटि";
    }
  };

  return (
    <div className="flex flex-col items-center justify-center py-8 sm:py-12 px-4 text-center">
      <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
        <div className="text-red-500 mb-4">
          {getErrorIcon()}
        </div>
        <h3 className="text-lg font-semibold text-red-800 mb-2">{getErrorTitle()}</h3>
        <p className="text-red-600 text-sm mb-4">{error}</p>
        <div className="flex flex-wrap gap-3 justify-center">
          {onRetry && (
            <Button 
              onClick={onRetry}
              variant="outline"
              className="border-red-300 hover:bg-red-50 text-red-600"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              पुनः प्रयास करें
            </Button>
          )}
          <Button 
            variant="outline"
            className="border-red-300 hover:bg-red-50 text-red-600"
            onClick={() => window.location.href = '/'}
          >
            होमपेज पर जाएं
          </Button>
        </div>
      </div>
    </div>
  );
};

// Progressive error handling component that doesn't block other content
export const EnhancedProgressiveError: React.FC<{
  error: string;
  onRetry?: () => void;
  className?: string;
  errorType?: 'network' | 'server' | 'timeout' | 'general';
  compact?: boolean;
}> = ({ error, onRetry, className = '', errorType = 'general', compact = false }) => {
  // Choose icon based on error type
  const getErrorIcon = () => {
    switch (errorType) {
      case 'network': return <WifiOff className={`${compact ? 'h-4 w-4' : 'h-5 w-5'} text-red-500 mr-2 flex-shrink-0 mt-0.5`} />;
      case 'server': return <Server className={`${compact ? 'h-4 w-4' : 'h-5 w-5'} text-red-500 mr-2 flex-shrink-0 mt-0.5`} />;
      case 'timeout': return <Clock className={`${compact ? 'h-4 w-4' : 'h-5 w-5'} text-red-500 mr-2 flex-shrink-0 mt-0.5`} />;
      default: return <AlertCircle className={`${compact ? 'h-4 w-4' : 'h-5 w-5'} text-red-500 mr-2 flex-shrink-0 mt-0.5`} />;
    }
  };

  // Get appropriate error title
  const getErrorTitle = () => {
    switch (errorType) {
      case 'network': return "नेटवर्क त्रुटि";
      case 'server': return "सर्वर त्रुटि";
      case 'timeout': return "टाइमआउट त्रुटि";
      default: return "लोड करने में त्रुटि";
    }
  };

  if (compact) {
    return (
      <div className={`bg-red-50 border border-red-100 rounded-md p-2 ${className}`}>
        <div className="flex items-center">
          {getErrorIcon()}
          <div className="flex-1">
            <p className="text-xs text-red-600">{error}</p>
          </div>
          {onRetry && (
            <button 
              onClick={onRetry}
              className="text-xs flex items-center text-red-600 hover:text-red-800 ml-2"
            >
              <RefreshCw className="mr-1 h-3 w-3" />
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-red-50 border border-red-100 rounded-lg p-4 ${className}`}>
      <div className="flex items-start">
        {getErrorIcon()}
        <div className="flex-1">
          <p className="text-sm text-red-800 font-medium mb-1">{getErrorTitle()}</p>
          <p className="text-xs text-red-600 mb-2">{error}</p>
          {onRetry && (
            <button 
              onClick={onRetry}
              className="text-xs flex items-center text-red-600 hover:text-red-800"
            >
              <RefreshCw className="mr-1 h-3 w-3" />
              पुनः प्रयास करें
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

// Information banner that can be used to show non-critical messages
export const InfoBanner: React.FC<{
  message: string;
  className?: string;
  onDismiss?: () => void;
}> = ({ message, className = '', onDismiss }) => (
  <div className={`bg-blue-50 border border-blue-100 rounded-lg p-3 ${className}`}>
    <div className="flex items-start">
      <Info className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5" />
      <div className="flex-1">
        <p className="text-sm text-blue-800">{message}</p>
      </div>
      {onDismiss && (
        <button 
          onClick={onDismiss}
          className="text-blue-400 hover:text-blue-600"
        >
          <span className="sr-only">Dismiss</span>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      )}
    </div>
  </div>
);

// Partial content error that shows when some data loaded but other parts failed
export const PartialContentError: React.FC<{
  message: string;
  onRetry?: () => void;
  className?: string;
}> = ({ message, onRetry, className = '' }) => (
  <div className={`bg-amber-50 border border-amber-100 rounded-lg p-3 ${className}`}>
    <div className="flex items-start">
      <AlertCircle className="h-5 w-5 text-amber-500 mr-2 flex-shrink-0 mt-0.5" />
      <div className="flex-1">
        <p className="text-sm text-amber-800 font-medium mb-1">आंशिक डेटा लोड हुआ</p>
        <p className="text-xs text-amber-700">{message}</p>
      </div>
      {onRetry && (
        <button 
          onClick={onRetry}
          className="text-xs flex items-center text-amber-600 hover:text-amber-800 ml-2"
        >
          <RefreshCw className="mr-1 h-3 w-3" />
          पुनः प्रयास करें
        </button>
      )}
    </div>
  </div>
);