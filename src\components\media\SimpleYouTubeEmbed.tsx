import React, { useState } from 'react';
import { Play, X } from 'lucide-react';

interface SimpleYouTubeEmbedProps {
  videoUrl: string;
  title: string;
  className?: string;
  thumbnailUrl?: string;
  aspectRatio?: 'square' | 'video' | 'portrait' | 'landscape';
}

export const SimpleYouTubeEmbed: React.FC<SimpleYouTubeEmbedProps> = ({
  videoUrl,
  title,
  className = '',
  thumbnailUrl,
  aspectRatio = 'video'
}) => {
  const [isPlaying, setIsPlaying] = useState(false);

  // Extract YouTube video ID - handles multiple URL formats
  const getVideoId = (url: string): string | null => {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^#&?]*)/,
      /youtube\.com\/v\/([^#&?]*)/,
      /youtube\.com\/.*[?&]v=([^#&?]*)/
    ];
    
    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match && match[1] && match[1].length === 11) {
        return match[1];
      }
    }
    
    return null;
  };

  const videoId = getVideoId(videoUrl);

  if (!videoId) {
    return (
      <div className="bg-red-100 p-4 text-red-800 rounded-lg">
        <p>Invalid YouTube URL: {videoUrl}</p>
        <p className="text-sm mt-1">Please use a valid YouTube link like:</p>
        <p className="text-xs">https://www.youtube.com/watch?v=VIDEO_ID</p>
      </div>
    );
  }

  if (isPlaying) {
    return (
      <div 
        className={`relative w-full bg-black rounded-lg overflow-hidden ${className}`}
        style={{ 
          aspectRatio: '16/9',
          minHeight: '250px',
          height: 'auto'
        }}
      >
        {/* Close button - responsive sizing */}
        <button
          onClick={() => setIsPlaying(false)}
          className="absolute top-2 right-2 sm:top-3 sm:right-3 z-30 w-8 h-8 sm:w-10 sm:h-10 bg-black bg-opacity-70 hover:bg-opacity-90 rounded-full flex items-center justify-center transition-all border border-white border-opacity-20 touch-manipulation"
          aria-label="Close video"
        >
          <X className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
        </button>
        
        {/* YouTube iframe - optimized for mobile */}
        <iframe
          key={videoId}
          src={`https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0&modestbranding=1&enablejsapi=1&playsinline=1`}
          title={title}
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          allowFullScreen
          className="w-full h-full"
          style={{ 
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            border: 'none',
            outline: 'none',
            minHeight: '250px'
          }}
        />
      </div>
    );
  }

  return (
    <div 
      className={`relative w-full bg-gray-200 rounded-lg overflow-hidden ${className}`}
      style={{ 
        aspectRatio: '16/9',
        minHeight: '250px',
        height: 'auto'
      }}
    >
      {/* Video thumbnail */}
      <img
        src={thumbnailUrl || `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`}
        alt={title}
        className="w-full h-full object-cover"
        style={{ minHeight: '250px' }}
        onError={(e) => {
          // Fallback to standard quality thumbnail if maxres fails
          e.currentTarget.src = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
        }}
      />
      
      {/* Play button - responsive sizing */}
      <button
        onClick={() => setIsPlaying(true)}
        className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 hover:bg-opacity-40 active:bg-opacity-50 transition-opacity group touch-manipulation"
        aria-label="Play video"
      >
        <div className="w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 bg-red-600 hover:bg-red-700 active:bg-red-800 rounded-full flex items-center justify-center transition-all group-hover:scale-110 group-active:scale-95 shadow-lg">
          <Play className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 text-white ml-0.5 sm:ml-1" fill="white" />
        </div>
      </button>
      
      {/* Video badge - responsive positioning */}
      <div className="absolute bottom-1 right-1 sm:bottom-2 sm:right-2 bg-black bg-opacity-70 text-white text-xs px-1.5 py-0.5 sm:px-2 sm:py-1 rounded">
        Video
      </div>
    </div>
  );
};