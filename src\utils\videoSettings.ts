// Video embedding preferences
export interface VideoSettings {
  embedInArticles: boolean;
  embedInCards: boolean;
  embedInGallery: boolean;
  autoPlay: boolean;
}

const DEFAULT_SETTINGS: VideoSettings = {
  embedInArticles: true,
  embedInCards: false,
  embedInGallery: true,
  autoPlay: false
};

const STORAGE_KEY = 'video-settings';

export const getVideoSettings = (): VideoSettings => {
  if (typeof window === 'undefined') return DEFAULT_SETTINGS;
  
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      return { ...DEFAULT_SETTINGS, ...JSON.parse(stored) };
    }
  } catch (error) {
    console.error('Error loading video settings:', error);
  }
  
  return DEFAULT_SETTINGS;
};

export const saveVideoSettings = (settings: Partial<VideoSettings>): void => {
  if (typeof window === 'undefined') return;
  
  try {
    const current = getVideoSettings();
    const updated = { ...current, ...settings };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
  } catch (error) {
    console.error('Error saving video settings:', error);
  }
};

export const resetVideoSettings = (): void => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.error('Error resetting video settings:', error);
  }
};