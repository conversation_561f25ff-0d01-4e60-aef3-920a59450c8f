/**
 * Utility functions for error handling and detection
 */

// Error type detection
export type ErrorType = 'network' | 'server' | 'timeout' | 'general';

/**
 * Detects the type of error based on the error message or object
 * @param error The error object or message
 * @returns The detected error type
 */
export function detectErrorType(error: any): ErrorType {
  const errorMessage = error?.message || error?.toString() || '';
  
  // Check for network errors
  if (
    errorMessage.includes('network') ||
    errorMessage.includes('offline') ||
    errorMessage.includes('connection') ||
    errorMessage.includes('ECONNREFUSED') ||
    errorMessage.includes('ENOTFOUND') ||
    errorMessage.includes('Failed to fetch')
  ) {
    return 'network';
  }
  
  // Check for server errors
  if (
    errorMessage.includes('500') ||
    errorMessage.includes('502') ||
    errorMessage.includes('503') ||
    errorMessage.includes('504') ||
    errorMessage.includes('server error') ||
    errorMessage.includes('internal error')
  ) {
    return 'server';
  }
  
  // Check for timeout errors
  if (
    errorMessage.includes('timeout') ||
    errorMessage.includes('timed out') ||
    errorMessage.includes('ETIMEDOUT')
  ) {
    return 'timeout';
  }
  
  // Default to general error
  return 'general';
}

/**
 * Gets a user-friendly error message based on the error
 * @param error The error object or message
 * @returns A user-friendly error message
 */
export function getUserFriendlyErrorMessage(error: any): string {
  const errorType = detectErrorType(error);
  const originalMessage = error?.message || error?.toString() || 'Unknown error occurred';
  
  switch (errorType) {
    case 'network':
      return 'नेटवर्क कनेक्शन में समस्या है। कृपया अपने इंटरनेट कनेक्शन की जांच करें और पुनः प्रयास करें।';
    case 'server':
      return 'सर्वर में समस्या है। हमारी टीम इस पर काम कर रही है। कृपया कुछ देर बाद पुनः प्रयास करें।';
    case 'timeout':
      return 'अनुरोध का समय समाप्त हो गया है। कृपया पुनः प्रयास करें।';
    default:
      // For general errors, use the original message if it's user-friendly, otherwise use a generic message
      if (originalMessage.includes('permission') || originalMessage.includes('access')) {
        return 'आपके पास इस सामग्री तक पहुंचने की अनुमति नहीं है।';
      } else if (originalMessage.includes('not found') || originalMessage.includes('404')) {
        return 'अनुरोधित सामग्री नहीं मिली।';
      } else if (originalMessage.includes('quota') || originalMessage.includes('limit')) {
        return 'अनुरोध सीमा पार हो गई है। कृपया कुछ देर बाद पुनः प्रयास करें।';
      }
      return 'कुछ गलत हो गया। कृपया पुनः प्रयास करें।';
  }
}

/**
 * Logs an error with additional context
 * @param error The error object
 * @param context Additional context about where the error occurred
 */
export function logError(error: any, context: string): void {
  const errorType = detectErrorType(error);
  console.error(`[${errorType.toUpperCase()}] Error in ${context}:`, error);
  
  // Here you could also send the error to a monitoring service like Sentry
}

/**
 * Handles an error by logging it and returning a user-friendly message
 * @param error The error object
 * @param context Additional context about where the error occurred
 * @returns A user-friendly error message
 */
export function handleError(error: any, context: string): string {
  logError(error, context);
  return getUserFriendlyErrorMessage(error);
}

/**
 * Creates a retry function with exponential backoff
 * @param fn The function to retry
 * @param maxRetries Maximum number of retries
 * @param baseDelay Base delay in milliseconds
 * @returns A function that will retry the original function
 */
export function createRetryFunction<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): () => Promise<T> {
  let retries = 0;
  
  const retry = async (): Promise<T> => {
    try {
      return await fn();
    } catch (error) {
      if (retries >= maxRetries) {
        throw error;
      }
      
      const delay = baseDelay * Math.pow(2, retries);
      retries++;
      
      console.log(`Retry ${retries}/${maxRetries} after ${delay}ms`);
      await new Promise(resolve => setTimeout(resolve, delay));
      
      return retry();
    }
  };
  
  return retry;
}