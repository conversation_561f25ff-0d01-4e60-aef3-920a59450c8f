import { detectErrorType, getUserFriendlyErrorMessage, logError } from './errorUtils';

/**
 * Interface for progressive error handling
 */
export interface ProgressiveErrorState {
  error: string | null;
  errorType: 'network' | 'server' | 'timeout' | 'general' | null;
  isCritical: boolean;
  retryCount: number;
  lastRetry: number | null;
}

/**
 * Creates an initial progressive error state
 */
export function createProgressiveErrorState(): ProgressiveErrorState {
  return {
    error: null,
    errorType: null,
    isCritical: false,
    retryCount: 0,
    lastRetry: null
  };
}

/**
 * Handles an error in a progressive way that doesn't block other content
 * @param error The error object or message
 * @param context The context where the error occurred
 * @param isCritical Whether this is a critical error that should block rendering
 * @returns A progressive error state object
 */
export function handleProgressiveError(
  error: any, 
  context: string,
  isCritical: boolean = false
): ProgressiveErrorState {
  // Log the error
  logError(error, context);
  
  // Get error type and user-friendly message
  const errorType = detectErrorType(error);
  const errorMessage = getUserFriendlyErrorMessage(error);
  
  return {
    error: errorMessage,
    errorType,
    isCritical,
    retryCount: 0,
    lastRetry: null
  };
}

/**
 * Updates a progressive error state for retry
 * @param errorState The current error state
 * @returns An updated error state with incremented retry count
 */
export function retryProgressiveError(errorState: ProgressiveErrorState): ProgressiveErrorState {
  return {
    ...errorState,
    retryCount: errorState.retryCount + 1,
    lastRetry: Date.now()
  };
}

/**
 * Clears a progressive error state
 * @param errorState The current error state
 * @returns A reset error state
 */
export function clearProgressiveError(): ProgressiveErrorState {
  return createProgressiveErrorState();
}

/**
 * Determines if a retry should be allowed based on the error state
 * @param errorState The current error state
 * @param maxRetries Maximum number of retries allowed
 * @param cooldownMs Cooldown period in milliseconds between retries
 * @returns Whether a retry should be allowed
 */
export function canRetry(
  errorState: ProgressiveErrorState,
  maxRetries: number = 3,
  cooldownMs: number = 5000
): boolean {
  if (!errorState.error) return false;
  if (errorState.retryCount >= maxRetries) return false;
  
  if (errorState.lastRetry) {
    const timeSinceLastRetry = Date.now() - errorState.lastRetry;
    if (timeSinceLastRetry < cooldownMs) return false;
  }
  
  return true;
}

/**
 * Creates a retry function with exponential backoff for progressive loading
 * @param fn The function to retry
 * @param onError Callback for error handling
 * @param maxRetries Maximum number of retries
 * @param baseDelay Base delay in milliseconds
 * @returns A function that will retry the original function
 */
export function createProgressiveRetryFunction<T>(
  fn: () => Promise<T>,
  onError: (error: any) => void,
  maxRetries: number = 3,
  baseDelay: number = 1000
): () => Promise<T> {
  let retries = 0;
  
  const retry = async (): Promise<T> => {
    try {
      return await fn();
    } catch (error) {
      onError(error);
      
      if (retries >= maxRetries) {
        throw error;
      }
      
      const delay = baseDelay * Math.pow(2, retries);
      retries++;
      
      console.log(`Progressive retry ${retries}/${maxRetries} after ${delay}ms`);
      await new Promise(resolve => setTimeout(resolve, delay));
      
      return retry();
    }
  };
  
  return retry;
}