import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';

// Enhanced skeleton for news card that better represents the actual content
export const EnhancedNewsCardSkeleton: React.FC = () => {
  return (
    <div className="border rounded-lg overflow-hidden bg-card transition-all hover:shadow-md">
      <div className="relative">
        <Skeleton className="w-full h-48 sm:h-56" />
        <div className="absolute top-2 left-2">
          <Skeleton className="h-5 w-16 rounded-md" />
        </div>
      </div>
      <div className="p-3 sm:p-4 space-y-2">
        <Skeleton className="h-6 w-4/5" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
        <div className="flex items-center justify-between pt-2">
          <div className="flex items-center">
            <Skeleton className="h-4 w-4 mr-1 rounded-full" />
            <Skeleton className="h-3 w-24" />
          </div>
          <div className="flex items-center">
            <Skeleton className="h-4 w-4 mr-1 rounded-full" />
            <Skeleton className="h-3 w-8" />
          </div>
        </div>
      </div>
    </div>
  );
};

// Enhanced skeleton for horizontal news card with better content representation
export const EnhancedNewsCardHorizontalSkeleton: React.FC = () => {
  return (
    <div className="border rounded-lg overflow-hidden bg-card transition-all hover:shadow-md">
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
        <div className="sm:col-span-1">
          <Skeleton className="h-full min-h-[150px] w-full" />
        </div>
        <div className="p-3 sm:p-4 sm:col-span-2 space-y-2">
          <Skeleton className="h-5 sm:h-6 w-4/5" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-2/3" />
          <div className="flex items-center justify-between pt-2">
            <div className="flex items-center">
              <Skeleton className="h-3 w-3 mr-1 rounded-full" />
              <Skeleton className="h-3 w-20" />
            </div>
            <div className="flex items-center">
              <Skeleton className="h-3 w-3 mr-1 rounded-full" />
              <Skeleton className="h-3 w-8" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Enhanced skeleton for article detail with better content representation
export const EnhancedArticleDetailSkeleton: React.FC = () => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 sm:gap-8">
      {/* Main Content */}
      <div className="lg:col-span-8">
        <article>
          {/* Category and Date */}
          <div className="flex flex-wrap items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
            <Skeleton className="h-6 w-20 rounded-md" />
            <div className="flex items-center">
              <Skeleton className="h-4 w-4 mr-1 rounded-full" />
              <Skeleton className="h-4 w-24" />
            </div>
          </div>

          {/* Title */}
          <Skeleton className="h-8 sm:h-10 w-full mb-2" />
          <Skeleton className="h-8 sm:h-10 w-4/5 mb-4" />

          {/* Author */}
          <div className="flex items-center mb-4 sm:mb-6">
            <Skeleton className="h-10 w-10 rounded-full mr-3" />
            <div>
              <Skeleton className="h-5 w-32 mb-1" />
              <Skeleton className="h-4 w-20" />
            </div>
          </div>

          {/* Featured Image */}
          <div className="mb-4 sm:mb-6">
            <Skeleton className="h-64 sm:h-80 lg:h-96 w-full rounded-lg" />
          </div>

          {/* Content */}
          <div className="space-y-3 mb-6">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-4/5" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>

          {/* Tags */}
          <div className="mb-6">
            <Skeleton className="h-5 w-20 mb-3" />
            <div className="flex flex-wrap gap-2">
              <Skeleton className="h-6 w-16 rounded-full" />
              <Skeleton className="h-6 w-20 rounded-full" />
              <Skeleton className="h-6 w-14 rounded-full" />
            </div>
          </div>
          
          {/* Share */}
          <div className="mb-8">
            <Skeleton className="h-5 w-24 mb-3" />
            <div className="flex gap-2">
              <Skeleton className="h-8 w-8 rounded-md" />
              <Skeleton className="h-8 w-8 rounded-md" />
              <Skeleton className="h-8 w-8 rounded-md" />
              <Skeleton className="h-8 w-8 rounded-md" />
            </div>
          </div>
          
          {/* Comments Section */}
          <div className="mb-8">
            <Skeleton className="h-6 w-32 mb-4" />
            <div className="space-y-4">
              <div className="border rounded-lg p-4">
                <div className="flex items-start gap-3 mb-3">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <div className="flex-1">
                    <Skeleton className="h-4 w-32 mb-1" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-4/5" />
              </div>
              <div className="border rounded-lg p-4">
                <div className="flex items-start gap-3 mb-3">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <div className="flex-1">
                    <Skeleton className="h-4 w-40 mb-1" />
                    <Skeleton className="h-3 w-20" />
                  </div>
                </div>
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/5" />
              </div>
            </div>
          </div>
        </article>
      </div>
      
      {/* Sidebar */}
      <div className="lg:col-span-4 mt-6 lg:mt-0">
        {/* Related News */}
        <div className="bg-muted/50 rounded-lg p-4 sm:p-6">
          <Skeleton className="h-6 w-40 mb-4" />
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index}>
                <div className="grid grid-cols-3 gap-2 sm:gap-3">
                  <Skeleton className="aspect-square rounded-md" />
                  <div className="col-span-2 space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                </div>
                {index < 2 && <Skeleton className="h-px w-full mt-4" />}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Enhanced loading state for search results
export const EnhancedSearchResultsSkeleton: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Skeleton className="h-7 w-48" />
        <Skeleton className="h-5 w-32" />
      </div>
      
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <EnhancedNewsCardHorizontalSkeleton key={index} />
        ))}
      </div>
    </div>
  );
};

// Enhanced loading state for category page with better content representation
export const EnhancedCategoryPageSkeleton: React.FC = () => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
      {/* Main Content */}
      <div className="lg:col-span-3">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 space-y-2 sm:space-y-0">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-40" />
        </div>

        <div className="grid grid-cols-1 gap-4 sm:gap-6">
          {Array.from({ length: 5 }).map((_, index) => (
            <EnhancedNewsCardHorizontalSkeleton key={index} />
          ))}
        </div>

        {/* Pagination */}
        <div className="mt-8 flex justify-center">
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-8 rounded-md" />
            <Skeleton className="h-8 w-8 rounded-md" />
            <Skeleton className="h-8 w-8 rounded-md" />
            <Skeleton className="h-8 w-8 rounded-md" />
            <Skeleton className="h-8 w-8 rounded-md" />
          </div>
        </div>
      </div>

      {/* Sidebar */}
      <div className="lg:col-span-1 mt-6 lg:mt-0">
        <div className="space-y-4 sm:space-y-6">
          <Skeleton className="h-60 w-full rounded-lg" />
          <Skeleton className="h-60 w-full rounded-lg" />
        </div>
      </div>
    </div>
  );
};

// Pulsating skeleton that draws more attention
export const PulsatingSkeletonLoader: React.FC<{
  message?: string;
  className?: string;
}> = ({ message = "लोड हो रहा है...", className = "" }) => {
  return (
    <div className={`flex flex-col items-center justify-center py-8 ${className}`}>
      <div className="relative w-16 h-16 mb-4">
        <div className="absolute inset-0 rounded-full border-4 border-muted"></div>
        <div className="absolute inset-0 rounded-full border-4 border-t-primary animate-spin"></div>
      </div>
      <p className="text-muted-foreground text-sm">{message}</p>
    </div>
  );
};

// Shimmer effect skeleton for a more dynamic loading experience
export const ShimmerSkeleton: React.FC<{
  height?: string;
  width?: string;
  className?: string;
}> = ({ height = "h-4", width = "w-full", className = "" }) => {
  return (
    <div className={`${height} ${width} rounded overflow-hidden ${className} relative`}>
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 shimmer-animation"></div>
      <style jsx>{`
        @keyframes shimmer {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(100%);
          }
        }
        .shimmer-animation {
          animation: shimmer 1.5s infinite;
        }
      `}</style>
    </div>
  );
};