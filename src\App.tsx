import { Toaster } from '@/components/ui/sonner';
import { TooltipProvider } from '@/components/ui/tooltip';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from '@/providers/theme-provider';
import { AuthProvider } from '@/contexts/AuthContext';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import './i18n'; // Initialize i18n

// Pages
import Index from './pages/Index';
import NewsDetail from './pages/NewsDetail';
import CategoryPage from './pages/CategoryPage';
import StateCategoryPage from './pages/StateCategoryPage';
import AboutPage from './pages/AboutPage';
import ContactPage from './pages/ContactPage';
import SearchResults from './pages/SearchResults';
import NotFound from './pages/NotFound';
import TagPage from './pages/TagPage';

// Admin Pages
import AdminLayout from './components/admin/AdminLayout';
import LoginPage from './pages/admin/LoginPage';
import DashboardPage from './pages/admin/DashboardPage';
import AdManagementPage from './pages/admin/AdManagementPage';
import NewsManagementPage from './pages/admin/NewsManagementPage';
import ContactManagementPage from './pages/admin/ContactManagementPage';
import UserManagementPage from './pages/admin/UserManagementPage';
import MigrationPage from './pages/admin/MigrationPage';



import CreateAccountPage from './pages/admin/CreateAccountPage';
import UnauthorizedPage from './pages/admin/UnauthorizedPage';

// Optimized React Query configuration for better caching strategy
// Mobile-optimized to reduce unnecessary refreshes
const isMobile = () => window.innerWidth < 768;

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Stale-while-revalidate pattern: show cached data immediately, then refetch in background
      staleTime: 5 * 60 * 1000, // 5 minutes - data is considered fresh for 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes - cache garbage collection time (formerly cacheTime)

      // Retry configuration for better reliability
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors (client errors)
        if (error && typeof error === 'object' && 'status' in error) {
          const status = (error as any).status;
          if (status >= 400 && status < 500) return false;
        }
        // Reduce retries on mobile to prevent multiple refreshes
        const maxRetries = isMobile() ? 1 : 2;
        return failureCount < maxRetries;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff

      // Background refetch settings - more conservative on mobile
      refetchOnWindowFocus: false, // Don't refetch when window regains focus (can be annoying)
      refetchOnReconnect: !isMobile(), // Only refetch on reconnect for desktop
      refetchOnMount: !isMobile(), // Only refetch on mount for desktop to prevent mobile refreshes
    },
    mutations: {
      // Retry mutations once on failure
      retry: 1,
      retryDelay: 1000,
    },
  },
});

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider defaultTheme="light">
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <BrowserRouter>
            <Routes>
              {/* Public Routes */}
              <Route path="/" element={<Index />} />
              <Route path="/news/:id" element={<NewsDetail />} />
              <Route path="/about" element={<AboutPage />} />
              <Route path="/contact" element={<ContactPage />} />
              <Route path="/search" element={<SearchResults />} />
              <Route path="/latest" element={<CategoryPage />} />
              <Route path="/national" element={<StateCategoryPage />} />
              <Route path="/national/:state" element={<StateCategoryPage />} />
              <Route path="/international" element={<CategoryPage />} />
              <Route path="/politics" element={<CategoryPage />} />
              <Route path="/sports" element={<CategoryPage />} />
              <Route path="/entertainment" element={<CategoryPage />} />
              <Route path="/technology" element={<CategoryPage />} />
              <Route path="/business" element={<CategoryPage />} />
              <Route path="/education" element={<CategoryPage />} />
              <Route path="/agriculture" element={<CategoryPage />} />
              <Route path="/special-reports" element={<CategoryPage />} />
              <Route path="/category/:category" element={<CategoryPage />} />
              <Route path="/tag/:tag" element={<TagPage />} />

              {/* Admin Authentication Routes */}
              <Route path="/admin/login" element={<LoginPage />} />
              <Route path="/admin/create-accounts" element={<CreateAccountPage />} />
              <Route path="/admin/unauthorized" element={<UnauthorizedPage />} />

              {/* Protected Admin Routes */}
              <Route path="/admin" element={
                <ProtectedRoute>
                  <AdminLayout />
                </ProtectedRoute>
              }>
                <Route index element={<DashboardPage />} />
                <Route path="news" element={
                  <ProtectedRoute requireEditor>
                    <NewsManagementPage />
                  </ProtectedRoute>
                } />
                <Route path="ads" element={
                  <ProtectedRoute requireAdmin>
                    <AdManagementPage />
                  </ProtectedRoute>
                } />
                <Route path="contacts" element={<ContactManagementPage />} />
                <Route path="users" element={
                  <ProtectedRoute requireAdmin>
                    <UserManagementPage />
                  </ProtectedRoute>
                } />
                <Route path="migration" element={
                  <ProtectedRoute requireAdmin>
                    <MigrationPage />
                  </ProtectedRoute>
                } />
              </Route>

              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </AuthProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
