import React, { useState, useEffect } from 'react';
import { EnhancedProgressiveError, PartialContentError } from '@/components/ui/enhanced-empty-states';
import { detectErrorType } from '@/utils/errorUtils';

interface ProgressiveErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  onError?: (error: Error) => void;
  onRetry?: () => void;
  isCritical?: boolean;
  compact?: boolean;
  className?: string;
}

/**
 * A component that catches errors in its children and displays a progressive error message
 * without blocking other content from rendering
 */
export const ProgressiveErrorBoundary: React.FC<ProgressiveErrorBoundaryProps> = ({
  children,
  fallback,
  onError,
  onRetry,
  isCritical = false,
  compact = false,
  className = ''
}) => {
  const [error, setError] = useState<Error | null>(null);
  const [hasError, setHasError] = useState(false);

  // Reset error state when children change
  useEffect(() => {
    setError(null);
    setHasError(false);
  }, [children]);

  // Error handler for child components
  const handleError = (error: Error) => {
    setError(error);
    setHasError(true);
    if (onError) {
      onError(error);
    }
  };

  // Handle retry action
  const handleRetry = () => {
    setError(null);
    setHasError(false);
    if (onRetry) {
      onRetry();
    }
  };

  // If there's an error, show the error UI
  if (hasError && error) {
    const errorType = detectErrorType(error.message);
    
    // If a custom fallback is provided, use it
    if (fallback) {
      return <>{fallback}</>;
    }
    
    // For critical errors, show a full error message
    if (isCritical) {
      return (
        <EnhancedProgressiveError
          error={error.message}
          errorType={errorType}
          onRetry={handleRetry}
          className={className}
          compact={compact}
        />
      );
    }
    
    // For non-critical errors, show a partial content error
    return (
      <PartialContentError
        message={`कुछ सामग्री लोड नहीं हो सकी: ${error.message}`}
        onRetry={handleRetry}
        className={className}
      />
    );
  }

  // Try to render children, catch any errors
  try {
    return <>{children}</>;
  } catch (error) {
    // Handle synchronous errors
    if (error instanceof Error) {
      handleError(error);
      
      if (fallback) {
        return <>{fallback}</>;
      }
      
      const errorType = detectErrorType(error.message);
      
      if (isCritical) {
        return (
          <EnhancedProgressiveError
            error={error.message}
            errorType={errorType}
            onRetry={handleRetry}
            className={className}
            compact={compact}
          />
        );
      }
      
      return (
        <PartialContentError
          message={`कुछ सामग्री लोड नहीं हो सकी: ${error.message}`}
          onRetry={handleRetry}
          className={className}
        />
      );
    }
    
    // For unknown errors
    return (
      <EnhancedProgressiveError
        error="अज्ञात त्रुटि हुई है"
        errorType="general"
        onRetry={handleRetry}
        className={className}
        compact={compact}
      />
    );
  }
};

/**
 * A component that wraps content with error handling and displays a skeleton while loading
 */
interface ProgressiveContentProps<T> {
  data: T | null | undefined;
  isLoading: boolean;
  error: any;
  onRetry?: () => void;
  loadingFallback: React.ReactNode;
  emptyFallback?: React.ReactNode;
  children: (data: T) => React.ReactNode;
  isCritical?: boolean;
  compact?: boolean;
  className?: string;
}

export function ProgressiveContent<T>({
  data,
  isLoading,
  error,
  onRetry,
  loadingFallback,
  emptyFallback,
  children,
  isCritical = false,
  compact = false,
  className = ''
}: ProgressiveContentProps<T>) {
  // Show loading state
  if (isLoading) {
    return <>{loadingFallback}</>;
  }
  
  // Show error state
  if (error) {
    const errorType = detectErrorType(error);
    
    if (isCritical) {
      return (
        <EnhancedProgressiveError
          error={error}
          errorType={errorType}
          onRetry={onRetry}
          className={className}
          compact={compact}
        />
      );
    }
    
    return (
      <PartialContentError
        message={`कुछ सामग्री लोड नहीं हो सकी: ${error}`}
        onRetry={onRetry}
        className={className}
      />
    );
  }
  
  // Show empty state
  if (!data || (Array.isArray(data) && data.length === 0)) {
    return emptyFallback ? <>{emptyFallback}</> : null;
  }
  
  // Show content with error boundary
  return (
    <ProgressiveErrorBoundary
      onRetry={onRetry}
      isCritical={isCritical}
      compact={compact}
      className={className}
    >
      {children(data)}
    </ProgressiveErrorBoundary>
  );
}