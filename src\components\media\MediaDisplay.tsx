import React from 'react';
import { Play } from 'lucide-react';
import { ImageWithSkeleton } from '@/components/ui/image-skeleton';
import { SimpleYouTubeEmbed } from './SimpleYouTubeEmbed';

interface MediaDisplayProps {
  imageUrl: string;
  videoUrl?: string;
  alt: string;
  aspectRatio?: 'square' | 'video' | 'portrait' | 'landscape';
  width?: number;
  height?: number;
  className?: string;
  showPlayButton?: boolean;
  embedVideo?: boolean; // New prop to control embedding vs external link
}

export const MediaDisplay: React.FC<MediaDisplayProps> = ({
  imageUrl,
  videoUrl,
  alt,
  aspectRatio = 'video',
  width = 800,
  height = 450,
  className = '',
  showPlayButton = false,
  embedVideo = true // Default to embedding videos
}) => {
  // Function to check if URL is a YouTube URL
  const isYouTubeUrl = (url?: string): boolean => {
    if (!url) return false;
    return url.includes('youtube.com') || url.includes('youtu.be');
  };
  
  const handlePlayClick = () => {
    if (videoUrl) {
      // Open YouTube video in a new tab
      window.open(videoUrl, '_blank');
    }
  };
  
  // If we have a YouTube video URL and embedding is enabled, use the YouTube embed component
  if (videoUrl && isYouTubeUrl(videoUrl) && embedVideo) {
    // Use simple embed for testing
    return (
      <SimpleYouTubeEmbed
        videoUrl={videoUrl}
        title={alt}
        className={className}
      />
    );
  }
  
  // Fallback to image with play button (for non-YouTube videos or when embedding is disabled)
  return (
    <div className={`relative ${className}`}>
      <ImageWithSkeleton
        src={imageUrl}
        alt={alt}
        aspectRatio={aspectRatio}
        width={width}
        height={height}
        className="w-full"
      />
      
      {videoUrl && showPlayButton && (
        <button
          onClick={handlePlayClick}
          className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 hover:bg-opacity-40 transition-opacity"
          aria-label="Play video"
        >
          <div className="w-16 h-16 sm:w-20 sm:h-20 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center transition-colors">
            <Play className="w-8 h-8 sm:w-10 sm:h-10 text-white ml-1" fill="white" />
          </div>
        </button>
      )}
    </div>
  );
};