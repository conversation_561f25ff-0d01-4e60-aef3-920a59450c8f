import React from 'react';
import { ShimmerSkeleton } from '@/components/ui/enhanced-skeletons';
import { InfoBanner } from '@/components/ui/enhanced-empty-states';

/**
 * Component that shows a content-aware loading state based on the content type
 * Provides better perceived performance with skeleton UI that matches the expected content
 */
export const ContentAwareLoading: React.FC<{
  contentType: 'article' | 'card' | 'list' | 'grid' | 'table' | 'comment' | 'form' | 'search';
  count?: number;
  showMessage?: boolean;
  message?: string;
  className?: string;
}> = ({ 
  contentType, 
  count = 1, 
  showMessage = false, 
  message = 'लोड हो रहा है...', 
  className = '' 
}) => {
  // Render appropriate loading skeleton based on content type
  const renderLoadingSkeleton = () => {
    switch (contentType) {
      case 'article':
        return (
          <div className="space-y-4">
            <ShimmerSkeleton height="h-8" width="w-3/4" className="mb-2" />
            <ShimmerSkeleton height="h-48" width="w-full" className="mb-4" />
            <ShimmerSkeleton height="h-4" width="w-full" />
            <ShimmerSkeleton height="h-4" width="w-full" />
            <ShimmerSkeleton height="h-4" width="w-4/5" />
            <ShimmerSkeleton height="h-4" width="w-full" />
            <ShimmerSkeleton height="h-4" width="w-3/4" />
          </div>
        );
        
      case 'card':
        return (
          <div className="border rounded-lg overflow-hidden bg-card transition-all hover:shadow-md">
            <div className="relative">
              <ShimmerSkeleton height="h-48" width="w-full" />
              <div className="absolute top-2 left-2">
                <ShimmerSkeleton height="h-5" width="w-16" className="rounded-md" />
              </div>
            </div>
            <div className="p-3 sm:p-4 space-y-2">
              <ShimmerSkeleton height="h-6" width="w-4/5" />
              <ShimmerSkeleton height="h-4" width="w-full" />
              <ShimmerSkeleton height="h-4" width="w-3/4" />
              <div className="flex items-center justify-between pt-2">
                <ShimmerSkeleton height="h-3" width="w-24" />
                <ShimmerSkeleton height="h-3" width="w-8" />
              </div>
            </div>
          </div>
        );
        
      case 'list':
        return (
          <div className="space-y-3">
            {Array.from({ length: count }).map((_, index) => (
              <div key={index} className="flex items-center gap-3 p-3 border-b">
                <ShimmerSkeleton height="h-10" width="w-10" className="rounded-full flex-shrink-0" />
                <div className="flex-1 space-y-2">
                  <ShimmerSkeleton height="h-4" width="w-full" />
                  <ShimmerSkeleton height="h-3" width="w-2/3" />
                </div>
                <ShimmerSkeleton height="h-8" width="w-16" className="rounded-md flex-shrink-0" />
              </div>
            ))}
          </div>
        );
        
      case 'grid':
        return (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {Array.from({ length: count }).map((_, index) => (
              <div key={index} className="space-y-2">
                <ShimmerSkeleton height="h-32" width="w-full" className="rounded-md" />
                <ShimmerSkeleton height="h-4" width="w-full" />
                <ShimmerSkeleton height="h-3" width="w-2/3" />
              </div>
            ))}
          </div>
        );
        
      case 'table':
        return (
          <div className="border rounded-md overflow-hidden">
            <div className="bg-muted/50 p-3">
              <div className="grid grid-cols-4 gap-4">
                <ShimmerSkeleton height="h-4" width="w-full" />
                <ShimmerSkeleton height="h-4" width="w-full" />
                <ShimmerSkeleton height="h-4" width="w-full" />
                <ShimmerSkeleton height="h-4" width="w-full" />
              </div>
            </div>
            <div className="divide-y">
              {Array.from({ length: count }).map((_, index) => (
                <div key={index} className="p-3">
                  <div className="grid grid-cols-4 gap-4">
                    <ShimmerSkeleton height="h-4" width="w-full" />
                    <ShimmerSkeleton height="h-4" width="w-full" />
                    <ShimmerSkeleton height="h-4" width="w-full" />
                    <ShimmerSkeleton height="h-4" width="w-full" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
        
      case 'comment':
        return (
          <div className="space-y-4">
            {Array.from({ length: count }).map((_, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-start gap-3 mb-3">
                  <ShimmerSkeleton height="h-8" width="w-8" className="rounded-full" />
                  <div className="flex-1">
                    <ShimmerSkeleton height="h-4" width="w-32" className="mb-1" />
                    <ShimmerSkeleton height="h-3" width="w-24" />
                  </div>
                </div>
                <ShimmerSkeleton height="h-4" width="w-full" />
                <ShimmerSkeleton height="h-4" width="w-4/5" />
              </div>
            ))}
          </div>
        );
        
      case 'form':
        return (
          <div className="space-y-4 border rounded-lg p-4">
            <ShimmerSkeleton height="h-6" width="w-1/3" className="mb-1" />
            <ShimmerSkeleton height="h-10" width="w-full" className="rounded-md" />
            
            <ShimmerSkeleton height="h-6" width="w-1/3" className="mb-1" />
            <ShimmerSkeleton height="h-10" width="w-full" className="rounded-md" />
            
            <ShimmerSkeleton height="h-6" width="w-1/3" className="mb-1" />
            <ShimmerSkeleton height="h-24" width="w-full" className="rounded-md" />
            
            <ShimmerSkeleton height="h-10" width="w-1/4" className="rounded-md mt-4" />
          </div>
        );
        
      case 'search':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <ShimmerSkeleton height="h-7" width="w-48" />
              <ShimmerSkeleton height="h-5" width="w-32" />
            </div>
            
            <div className="space-y-4">
              {Array.from({ length: count }).map((_, index) => (
                <div key={index} className="border rounded-lg overflow-hidden bg-card">
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
                    <div className="sm:col-span-1">
                      <ShimmerSkeleton height="h-full min-h-[150px]" width="w-full" />
                    </div>
                    <div className="p-3 sm:p-4 sm:col-span-2 space-y-2">
                      <ShimmerSkeleton height="h-5 sm:h-6" width="w-4/5" />
                      <ShimmerSkeleton height="h-4" width="w-full" />
                      <ShimmerSkeleton height="h-4" width="w-2/3" />
                      <div className="flex items-center justify-between pt-2">
                        <ShimmerSkeleton height="h-3" width="w-20" />
                        <ShimmerSkeleton height="h-3" width="w-8" />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
        
      default:
        return (
          <div className="space-y-4">
            <ShimmerSkeleton height="h-4" width="w-full" />
            <ShimmerSkeleton height="h-4" width="w-full" />
            <ShimmerSkeleton height="h-4" width="w-4/5" />
          </div>
        );
    }
  };

  return (
    <div className={className}>
      {showMessage && (
        <InfoBanner 
          message={message} 
          className="mb-4"
        />
      )}
      {renderLoadingSkeleton()}
    </div>
  );
};

/**
 * Component that shows a pulsating loading indicator with a message
 */
export const PulsatingLoader: React.FC<{
  message?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}> = ({ 
  message = 'लोड हो रहा है...', 
  className = '',
  size = 'md'
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  };
  
  return (
    <div className={`flex flex-col items-center justify-center py-6 ${className}`}>
      <div className={`relative ${sizeClasses[size]} mb-3`}>
        <div className="absolute inset-0 rounded-full border-4 border-muted"></div>
        <div className="absolute inset-0 rounded-full border-4 border-t-primary animate-spin"></div>
      </div>
      {message && <p className="text-muted-foreground text-sm">{message}</p>}
    </div>
  );
};

/**
 * Component that shows a loading state with a progress indicator
 */
export const ProgressIndicator: React.FC<{
  progress: number;
  message?: string;
  className?: string;
}> = ({ 
  progress, 
  message = 'लोड हो रहा है...', 
  className = '' 
}) => {
  // Ensure progress is between 0 and 100
  const normalizedProgress = Math.min(100, Math.max(0, progress));
  
  return (
    <div className={`flex flex-col items-center justify-center py-6 ${className}`}>
      <div className="w-full max-w-xs mb-3">
        <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
          <div 
            className="h-full bg-primary transition-all duration-300 ease-in-out"
            style={{ width: `${normalizedProgress}%` }}
          ></div>
        </div>
      </div>
      <div className="flex items-center justify-between w-full max-w-xs">
        <p className="text-muted-foreground text-sm">{message}</p>
        <span className="text-sm font-medium">{Math.round(normalizedProgress)}%</span>
      </div>
    </div>
  );
};