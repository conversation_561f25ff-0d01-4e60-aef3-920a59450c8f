import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { newsService, adService, userService, videoService, galleryService, breakingNewsService } from '@/services/firebaseService';
import { NewsArticle, Advertisement, User, PaginationData, Video, GalleryImage, BreakingNews } from '@/types';

// Query keys for consistent caching
export const queryKeys = {
  news: {
    all: ['news'] as const,
    lists: () => [...queryKeys.news.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.news.lists(), filters] as const,
    details: () => [...queryKeys.news.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.news.details(), id] as const,
    featured: () => [...queryKeys.news.all, 'featured'] as const,
    critical: () => [...queryKeys.news.all, 'critical'] as const,
    categories: () => [...queryKeys.news.all, 'categories'] as const,
    category: (category: string) => [...queryKeys.news.categories(), category] as const,
    trending: () => [...queryKeys.news.all, 'trending'] as const,
    mostViewed: () => [...queryKeys.news.all, 'mostViewed'] as const,
  },
  videos: {
    all: ['videos'] as const,
    featured: () => [...queryKeys.videos.all, 'featured'] as const,
  },
  gallery: {
    all: ['gallery'] as const,
    featured: () => [...queryKeys.gallery.all, 'featured'] as const,
  },
  ads: {
    all: ['ads'] as const,
    byPosition: (position: string, category?: string) => [...queryKeys.ads.all, 'position', position, category] as const,
  },
  breakingNews: {
    all: ['breakingNews'] as const,
    active: () => [...queryKeys.breakingNews.all, 'active'] as const,
  },
  users: {
    all: ['users'] as const,
  },
} as const;

// Cache time configurations for different content types
const CACHE_TIMES = {
  // Critical content - shorter cache for freshness
  critical: {
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  },
  // News content - moderate cache time
  news: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  },
  // Static-ish content - longer cache time
  categories: {
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  },
  // Enhancement content - longer cache, less critical
  enhancement: {
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 20 * 60 * 1000, // 20 minutes
  },
  // Breaking news - very short cache for urgency
  breaking: {
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
  },
} as const;

// Hook for news data with React Query
export const useNewsQuery = (
  category?: string, 
  status: 'published' | 'draft' | 'all' = 'published', 
  state?: string, 
  initialLimit: number = 5
) => {
  return useQuery({
    queryKey: queryKeys.news.list({ category, status, state, limit: initialLimit }),
    queryFn: async (): Promise<PaginationData<NewsArticle>> => {
      console.log(`Fetching news with React Query - category: ${category}, limit: ${initialLimit}`);
      const startTime = performance.now();
      
      const data = await newsService.getNews(initialLimit, undefined, category, status, state);
      
      const endTime = performance.now();
      console.log(`News loaded via React Query in ${(endTime - startTime).toFixed(2)}ms`);
      
      return data;
    },
    ...CACHE_TIMES.news,
    // Enable background refetch for news content
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });
};

// Hook for single news article with React Query
export const useNewsArticleQuery = (id: string) => {
  return useQuery({
    queryKey: queryKeys.news.detail(id),
    queryFn: async (): Promise<NewsArticle> => {
      console.log(`Fetching article ${id} with React Query`);
      const data = await newsService.getNewsById(id);
      if (!data) {
        throw new Error('Article not found');
      }
      return data;
    },
    ...CACHE_TIMES.news,
    enabled: !!id, // Only run query if id is provided
  });
};

// Hook for featured news with React Query
export const useFeaturedNewsQuery = (limit: number = 1) => {
  return useQuery({
    queryKey: queryKeys.news.featured(),
    queryFn: async (): Promise<NewsArticle[]> => {
      console.log(`Fetching featured news with React Query - limit: ${limit}`);
      const startTime = performance.now();
      
      const data = await newsService.getFeaturedNews(limit);
      
      const endTime = performance.now();
      console.log(`Featured news loaded via React Query in ${(endTime - startTime).toFixed(2)}ms`);
      
      return data;
    },
    ...CACHE_TIMES.critical, // Use critical cache times for featured content
  });
};

// Hook for critical content (parallel loading of news and featured news)
export const useCriticalContentQuery = (newsLimit: number = 5, featuredLimit: number = 1) => {
  return useQuery({
    queryKey: queryKeys.news.critical(),
    queryFn: async () => {
      console.log(`Fetching critical content with React Query - newsLimit: ${newsLimit}, featuredLimit: ${featuredLimit}`);
      const startTime = performance.now();
      
      // Use Promise.all for parallel loading
      const [newsData, featuredNewsData] = await Promise.all([
        newsService.getNews(newsLimit),
        newsService.getFeaturedNews(featuredLimit)
      ]);
      
      const endTime = performance.now();
      console.log(`Critical content loaded via React Query in ${(endTime - startTime).toFixed(2)}ms`);
      
      return {
        news: newsData,
        featuredNews: featuredNewsData
      };
    },
    ...CACHE_TIMES.critical,
    // High priority for critical content
    refetchOnMount: true,
  });
};

// Hook for news by categories with lazy loading
export const useNewsByCategoriesQuery = (enabled: boolean = true) => {
  return useQuery({
    queryKey: queryKeys.news.categories(),
    queryFn: async () => {
      console.log('Fetching categories list with React Query');
      const startTime = performance.now();

      // Import custom categories service
      const { getCustomCategories } = await import('@/services/categoryService');

      // Get default categories
      const defaultCategories = ['national', 'international', 'politics', 'sports', 'entertainment', 'technology', 'business', 'education', 'agriculture', 'special'];

      // Fetch custom categories
      const customCategories = await getCustomCategories();

      // Combine default and custom categories
      const allCategories = [...defaultCategories, ...customCategories];
      
      const endTime = performance.now();
      console.log(`Categories loaded via React Query in ${(endTime - startTime).toFixed(2)}ms`);
      
      return {
        categories: allCategories,
        categorizedNews: {} as Record<string, NewsArticle[]>, // Initialize empty
      };
    },
    ...CACHE_TIMES.categories,
    enabled,
  });
};

// Hook for specific category news (lazy loaded)
export const useCategoryNewsQuery = (category: string, enabled: boolean = false) => {
  return useQuery({
    queryKey: queryKeys.news.category(category),
    queryFn: async (): Promise<NewsArticle[]> => {
      console.log(`Lazy loading news for category: ${category} with React Query`);
      const startTime = performance.now();
      
      const data = await newsService.getNews(6, undefined, category, 'published');
      
      const endTime = performance.now();
      console.log(`Category ${category} loaded via React Query in ${(endTime - startTime).toFixed(2)}ms`);
      
      return data.items;
    },
    ...CACHE_TIMES.news,
    enabled: enabled && !!category,
  });
};

// Hook for trending tags with React Query
export const useTrendingTagsQuery = (enabled: boolean = true, delay: number = 0) => {
  return useQuery({
    queryKey: queryKeys.news.trending(),
    queryFn: async (): Promise<Array<{name: string, count: number}>> => {
      console.log('Fetching trending tags with React Query');
      const startTime = performance.now();
      
      // Add delay if specified (for progressive loading)
      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
      
      const data = await newsService.getNews(50, undefined, undefined, 'published');
      
      // Process tags efficiently
      const tagCounts: Record<string, number> = {};
      
      data.items.forEach(article => {
        if (article.tags && Array.isArray(article.tags)) {
          article.tags.forEach(tag => {
            if (tag && typeof tag === 'string') {
              tagCounts[tag] = (tagCounts[tag] || 0) + 1;
            }
          });
        }
      });

      const sortedTags = Object.entries(tagCounts)
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      const endTime = performance.now();
      console.log(`Trending tags loaded via React Query in ${(endTime - startTime).toFixed(2)}ms`);
      
      return sortedTags;
    },
    ...CACHE_TIMES.enhancement,
    enabled,
  });
};

// Hook for most viewed news with React Query
export const useMostViewedNewsQuery = (limit: number = 5, enabled: boolean = true) => {
  return useQuery({
    queryKey: queryKeys.news.mostViewed(),
    queryFn: async (): Promise<NewsArticle[]> => {
      console.log(`Fetching most viewed news with React Query - limit: ${limit}`);
      const data = await newsService.getMostViewedNews(limit);
      return data;
    },
    ...CACHE_TIMES.news,
    enabled,
  });
};

// Hook for most viewed news this week with React Query
export const useMostViewedNewsThisWeekQuery = (limit: number = 10, enabled: boolean = true) => {
  return useQuery({
    queryKey: [...queryKeys.news.all, 'mostViewedThisWeek', { limit }],
    queryFn: async (): Promise<NewsArticle[]> => {
      console.log(`Fetching most viewed news this week with React Query - limit: ${limit}`);
      const data = await newsService.getMostViewedNewsThisWeek(limit);
      return data;
    },
    ...CACHE_TIMES.news,
    enabled,
  });
};

// Hook for latest news by date (today/yesterday) with React Query
export const useLatestNewsByDateQuery = (limit: number = 15, category?: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: [...queryKeys.news.all, 'latestByDate', { limit, category }],
    queryFn: async (): Promise<NewsArticle[]> => {
      console.log(`Fetching latest news by date with React Query - limit: ${limit}, category: ${category}`);
      const startTime = performance.now();
      
      const data = await newsService.getLatestNewsByDate(limit, category, 'published');
      
      const endTime = performance.now();
      console.log(`Latest news by date loaded via React Query in ${(endTime - startTime).toFixed(2)}ms`);
      
      return data;
    },
    ...CACHE_TIMES.critical, // Use critical cache times for ticker content
    enabled,
    // Refetch more frequently for ticker to ensure fresh content
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
};

// Hook for videos with React Query
export const useVideosQuery = (enabled: boolean = true) => {
  return useQuery({
    queryKey: queryKeys.videos.featured(),
    queryFn: async (): Promise<Video[]> => {
      console.log('Fetching videos with React Query');
      const data = await videoService.getFeaturedVideos(6);
      return data;
    },
    ...CACHE_TIMES.enhancement,
    enabled,
  });
};

// Hook for gallery images with React Query
export const useGalleryImagesQuery = (enabled: boolean = true) => {
  return useQuery({
    queryKey: queryKeys.gallery.featured(),
    queryFn: async (): Promise<GalleryImage[]> => {
      console.log('Fetching gallery images with React Query');
      const data = await galleryService.getFeaturedImages(6);
      return data;
    },
    ...CACHE_TIMES.enhancement,
    enabled,
  });
};

// Hook for breaking news with React Query
export const useBreakingNewsQuery = () => {
  return useQuery({
    queryKey: queryKeys.breakingNews.active(),
    queryFn: async (): Promise<BreakingNews[]> => {
      console.log('Fetching breaking news with React Query');
      const data = await breakingNewsService.getActiveBreakingNews();
      return data;
    },
    ...CACHE_TIMES.breaking, // Very short cache for breaking news
    // Refetch breaking news more frequently
    refetchInterval: 60 * 1000, // Refetch every minute
    refetchIntervalInBackground: true,
  });
};

// Hook for advertisements with React Query
export const useAdsQuery = (position: Advertisement['position'], category?: string) => {
  return useQuery({
    queryKey: queryKeys.ads.byPosition(position, category),
    queryFn: async (): Promise<Advertisement[]> => {
      console.log(`Fetching ads for position: ${position}, category: ${category} with React Query`);
      const data = await adService.getAdsByPosition(position, category);

      // Filter ads by date range
      const now = new Date();
      const activeAds = data.filter(ad => {
        const isActive = ad.isActive;
        const startDate = new Date(ad.startDate);
        const endDate = new Date(ad.endDate);
        const currentDate = new Date();

        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);
        currentDate.setHours(0, 0, 0, 0);

        const adjustedStartDate = new Date(startDate);
        adjustedStartDate.setDate(adjustedStartDate.getDate() - 2);

        const isInDateRange = currentDate >= adjustedStartDate && currentDate <= endDate;
        return isActive && isInDateRange;
      });

      return activeAds;
    },
    ...CACHE_TIMES.enhancement,
  });
};

// Hook for all advertisements (admin) with React Query
export const useAllAdsQuery = (enabled: boolean = true) => {
  return useQuery({
    queryKey: queryKeys.ads.all,
    queryFn: async (): Promise<Advertisement[]> => {
      const data = await adService.getAllAds();
      return data;
    },
    ...CACHE_TIMES.news,
    enabled,
  });
};

// Hook for users (admin) with React Query
export const useUsersQuery = () => {
  return useQuery({
    queryKey: queryKeys.users.all,
    queryFn: async (): Promise<User[]> => {
      const data = await userService.getAllUsers();
      return data;
    },
    ...CACHE_TIMES.news,
  });
};

// Hook for all breaking news (admin) with React Query
export const useAllBreakingNewsQuery = () => {
  return useQuery({
    queryKey: queryKeys.breakingNews.all,
    queryFn: async (): Promise<BreakingNews[]> => {
      const data = await breakingNewsService.getAllBreakingNews();
      return data;
    },
    ...CACHE_TIMES.news,
  });
};

// Utility hook to prefetch category news
export const usePrefetchCategoryNews = () => {
  const queryClient = useQueryClient();
  
  const prefetchCategoryNews = async (category: string) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.news.category(category),
      queryFn: async (): Promise<NewsArticle[]> => {
        console.log(`Prefetching category ${category} in the background`);
        const data = await newsService.getNews(6, undefined, category, 'published');
        return data.items;
      },
      ...CACHE_TIMES.news,
    });
  };

  return { prefetchCategoryNews };
};

// Mutation hooks for CRUD operations
export const useCreateNewsMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (article: Omit<NewsArticle, 'id' | 'createdAt' | 'updatedAt'>) =>
      newsService.createNews(article),
    onSuccess: () => {
      // Invalidate and refetch news queries
      queryClient.invalidateQueries({ queryKey: queryKeys.news.all });
    },
  });
};

export const useUpdateNewsMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<NewsArticle> }) =>
      newsService.updateNews(id, updates),
    onSuccess: (_, { id }) => {
      // Invalidate specific article and news lists
      queryClient.invalidateQueries({ queryKey: queryKeys.news.detail(id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.news.lists() });
    },
  });
};

export const useDeleteNewsMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => newsService.deleteNews(id),
    onSuccess: () => {
      // Invalidate all news queries
      queryClient.invalidateQueries({ queryKey: queryKeys.news.all });
    },
  });
};