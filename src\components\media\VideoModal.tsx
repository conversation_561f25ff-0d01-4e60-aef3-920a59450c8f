import React from 'react';
import { X } from 'lucide-react';
import { YouTubeEmbed } from './YouTubeEmbed';

interface VideoModalProps {
  isOpen: boolean;
  onClose: () => void;
  videoUrl: string;
  title: string;
}

export const VideoModal: React.FC<VideoModalProps> = ({
  isOpen,
  onClose,
  videoUrl,
  title
}) => {
  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 p-4"
      onClick={handleBackdropClick}
    >
      <div className="relative w-full max-w-4xl mx-auto">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute -top-10 right-0 w-8 h-8 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full flex items-center justify-center transition-opacity z-10"
          aria-label="Close video"
        >
          <X className="w-5 h-5 text-white" />
        </button>
        
        {/* Video container */}
        <div className="bg-black rounded-lg overflow-hidden">
          <YouTubeEmbed
            videoUrl={videoUrl}
            title={title}
            aspectRatio="video"
            className="w-full"
            autoPlay={true}
          />
        </div>
      </div>
    </div>
  );
};