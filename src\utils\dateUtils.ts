// Date utility functions for news filtering

/**
 * Get the date that is 24 hours ago from now
 */
export const getTwentyFourHoursAgo = (): Date => {
  const now = new Date();
  return new Date(now.getTime() - (24 * 60 * 60 * 1000));
};

/**
 * Get the date that is 7 days (1 week) ago from now
 */
export const getSevenDaysAgo = (): Date => {
  const now = new Date();
  return new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
};

/**
 * Check if a date is within the last 24 hours
 */
export const isWithinLast24Hours = (date: Date): boolean => {
  const twentyFourHoursAgo = getTwentyFourHoursAgo();
  return date >= twentyFourHoursAgo;
};

/**
 * Check if a date is within the last 7 days (1 week)
 */
export const isWithinLastWeek = (date: Date): boolean => {
  const sevenDaysAgo = getSevenDaysAgo();
  return date >= sevenDaysAgo;
};

/**
 * Get today's date range (start and end of today)
 */
export const getTodayRange = (): { start: Date; end: Date } => {
  const today = new Date();
  const start = new Date(today);
  start.setHours(0, 0, 0, 0);
  
  const end = new Date(today);
  end.setHours(23, 59, 59, 999);
  
  return { start, end };
};

/**
 * Get yesterday's date range (start and end of yesterday)
 */
export const getYesterdayRange = (): { start: Date; end: Date } => {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  
  const start = new Date(yesterday);
  start.setHours(0, 0, 0, 0);
  
  const end = new Date(yesterday);
  end.setHours(23, 59, 59, 999);
  
  return { start, end };
};

/**
 * Format date for logging purposes
 */
export const formatDateForLog = (date: Date): string => {
  return date.toLocaleString('hi-IN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

/**
 * Check if an article should be considered "latest" based on 24-hour rule
 */
export const isLatestArticle = (articleDate: Date): boolean => {
  return isWithinLast24Hours(articleDate);
};

/**
 * Check if an article should be considered "featured" based on 24-hour rule
 */
export const isFeaturedArticle = (articleDate: Date, isFeatured: boolean): boolean => {
  return isFeatured && isWithinLast24Hours(articleDate);
};