import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { AlertCircle, FileQuestion, RefreshCw, Search, Newspaper, Video, Image, Tag } from 'lucide-react';
import { Link } from 'react-router-dom';

interface EmptyStateProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  action?: {
    label: string;
    onClick?: () => void;
    href?: string;
  };
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  icon,
  action
}) => {
  return (
    <div className="flex flex-col items-center justify-center py-8 sm:py-12 px-4 text-center">
      <div className="bg-muted/50 rounded-full p-4 mb-4">
        {icon || <FileQuestion className="h-8 w-8 text-muted-foreground" />}
      </div>
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground mb-6 max-w-md">{description}</p>
      {action && (
        action.href ? (
          <Link to={action.href}>
            <Button>{action.label}</Button>
          </Link>
        ) : (
          <Button onClick={action.onClick}>{action.label}</Button>
        )
      )}
    </div>
  );
};

// Specific empty states for different content types
export const NewsEmptyState: React.FC<{ onRetry?: () => void }> = ({ onRetry }) => (
  <EmptyState
    title="कोई समाचार नहीं मिला"
    description="इस समय कोई समाचार उपलब्ध नहीं है। कृपया बाद में पुनः प्रयास करें।"
    icon={<Newspaper className="h-8 w-8 text-muted-foreground" />}
    action={onRetry ? { label: "पुनः प्रयास करें", onClick: onRetry } : undefined}
  />
);

export const CategoryEmptyState: React.FC<{ category: string }> = ({ category }) => (
  <EmptyState
    title={`${category} में कोई समाचार नहीं`}
    description="इस श्रेणी में अभी कोई समाचार उपलब्ध नहीं है। कृपया अन्य श्रेणियां देखें।"
    icon={<FileQuestion className="h-8 w-8 text-muted-foreground" />}
    action={{ label: "सभी समाचार देखें", href: "/latest" }}
  />
);

export const SearchEmptyState: React.FC<{ query: string }> = ({ query }) => (
  <EmptyState
    title="कोई परिणाम नहीं मिला"
    description={`"${query}" के लिए कोई परिणाम नहीं मिला। कृपया अन्य कीवर्ड के साथ खोजें।`}
    icon={<Search className="h-8 w-8 text-muted-foreground" />}
  />
);

export const VideosEmptyState: React.FC = () => (
  <EmptyState
    title="कोई वीडियो नहीं मिला"
    description="इस समय कोई वीडियो उपलब्ध नहीं है।"
    icon={<Video className="h-8 w-8 text-muted-foreground" />}
  />
);

export const GalleryEmptyState: React.FC = () => (
  <EmptyState
    title="कोई छवि नहीं मिली"
    description="इस समय फोटो गैलरी में कोई छवि उपलब्ध नहीं है।"
    icon={<Image className="h-8 w-8 text-muted-foreground" />}
  />
);

export const TagsEmptyState: React.FC = () => (
  <EmptyState
    title="कोई ट्रेंडिंग टैग नहीं"
    description="इस समय कोई ट्रेंडिंग टैग उपलब्ध नहीं है।"
    icon={<Tag className="h-8 w-8 text-muted-foreground" />}
  />
);

export const ErrorState: React.FC<{ 
  error: string;
  onRetry?: () => void;
}> = ({ error, onRetry }) => (
  <div className="flex flex-col items-center justify-center py-8 sm:py-12 px-4 text-center">
    <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
      <div className="text-red-500 mb-4">
        <AlertCircle className="h-10 w-10 mx-auto" />
      </div>
      <h3 className="text-lg font-semibold text-red-800 mb-2">त्रुटि</h3>
      <p className="text-red-600 text-sm mb-4">{error}</p>
      {onRetry && (
        <Button 
          onClick={onRetry}
          variant="outline"
          className="border-red-300 hover:bg-red-50 text-red-600"
        >
          <RefreshCw className="mr-2 h-4 w-4" />
          पुनः प्रयास करें
        </Button>
      )}
    </div>
  </div>
);

// Progressive error handling component that doesn't block other content
export const ProgressiveError: React.FC<{
  error: string;
  onRetry?: () => void;
  className?: string;
}> = ({ error, onRetry, className = '' }) => (
  <div className={`bg-red-50 border border-red-100 rounded-lg p-4 ${className}`}>
    <div className="flex items-start">
      <AlertCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
      <div className="flex-1">
        <p className="text-sm text-red-800 font-medium mb-1">लोड करने में त्रुटि</p>
        <p className="text-xs text-red-600 mb-2">{error}</p>
        {onRetry && (
          <button 
            onClick={onRetry}
            className="text-xs flex items-center text-red-600 hover:text-red-800"
          >
            <RefreshCw className="mr-1 h-3 w-3" />
            पुनः प्रयास करें
          </button>
        )}
      </div>
    </div>
  </div>
);