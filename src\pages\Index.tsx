import { useTranslation } from "react-i18next";
import { Layout } from "@/components/layout/layout";
import { NewsCardLarge } from "@/components/news/news-card-large";
import { NewsCardHorizontal } from "@/components/news/news-card-horizontal";
import { NewsCardSmall } from "@/components/news/news-card-small";

import { AdContainer } from "@/components/ads/AdContainer";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ChevronRight } from "lucide-react";
import { useAllAds } from "@/hooks/useFirebaseData";
import { Link } from "react-router-dom";
import {
  useCriticalContent,
  useNewsByCategories,
  useTrendingTags,
  useVideos,
  useGalleryImages,
  useMostViewedNewsThisWeek,
  useLatestNewsByDate
} from "@/hooks/useFirebaseData";
import { getCategoryUrl, getCategoryDisplayName } from "@/utils/categoryMappings";
import { useTranslatedArticles } from "@/hooks/useTranslatedContent";
import { useInfiniteScroll, useInfiniteScrollObserver } from "@/hooks/useInfiniteScroll";
import { NewsCardSkeleton } from "@/components/ui/image-skeleton";
import {
  TrendingTagsSkeleton,
  CategoryContentSkeleton,
  VideosSkeleton,
  PhotoGallerySkeleton,
  PopularArticlesSkeleton,
  ProgressiveHomeSkeleton
} from "@/components/ui/skeleton-loaders";
import { detectErrorType } from "@/utils/errorUtils";
import { useState, useEffect, useMemo, useCallback, useRef } from "react";
import { AdaptiveEmptyState, AdaptiveErrorState } from '@/components/ui/adaptive-empty-states';
import { DataLoader } from '@/components/ui/data-loader';
import { ProgressiveErrorBoundary } from '@/components/ui/progressive-error-boundary';


// Type definitions for better TypeScript support
interface Article {
  id: string;
  title: string;
  excerpt?: string;
  category: string;
  imageUrl?: string;
  publishedAt: string;
  commentsCount?: number;
  views?: number;
}

interface TrendingTag {
  name: string;
  count: number;
}

// Helper function to safely get the last selected category from localStorage
function getLastSelectedCategory(): string {
  try {
    const savedCategory = localStorage.getItem('lastSelectedCategory');
    return savedCategory || "national";
  } catch (err) {
    console.error('Error reading from localStorage:', err);
    return "national";
  }
}

// Progressive loading phases
type LoadingPhase = 'critical' | 'secondary' | 'enhancement' | 'complete';

// Loading state interface for progressive loading
interface ProgressiveLoadingState {
  phase: LoadingPhase;
  criticalLoaded: boolean;
  secondaryLoaded: boolean;
  enhancementLoaded: boolean;
  error: string | null;
}

export default function Index(): JSX.Element {
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language as 'hi' | 'en';

  // Create a ref for the category section
  const categorySectionRef = useRef<HTMLDivElement>(null);

  // Progressive loading state management
  const [loadingState, setLoadingState] = useState<ProgressiveLoadingState>({
    phase: 'critical',
    criticalLoaded: false,
    secondaryLoaded: false,
    enhancementLoaded: false,
    error: null
  });

  // Fetch critical content performance
  const {
    news: latestNewsData,
    featuredNews,
    loading: criticalLoading,
    error: criticalError
  } = useCriticalContent(5, 1);

  // Fetch latest news by date (today first, then yesterday) for the "ताज़ा खबरें" section
  const {
    latestNews: todayLatestNews,
    loading: todayLatestLoading,
    error: todayLatestError
  } = useLatestNewsByDate(
    10,
    undefined,
    loadingState.phase !== 'critical'
  );

  // Derived error states for backward compatibility
  const newsError = criticalError;
  const featuredError = criticalError;

  // Lazy load secondary data based on loading phase
  const {
    categorizedNews,
    loading: categorizedLoading,
    categories,
    error: categorizedError,
    fetchCategoryNews,
    loadingCategories,
    prefetchCategoryNews,
    categoriesLoaded
  } = useNewsByCategories(loadingState.phase !== 'critical');

  // Memoize category display names to prevent blinking in "No articles found" messages
  const categoryDisplayNames = useMemo(() => {
    const names: Record<string, string> = {};
    categories.forEach(category => {
      names[category] = getCategoryDisplayName(category, currentLanguage);
    });
    return names;
  }, [categories, currentLanguage]);

  // Load trending tags with a delay to ensure they don't block critical content
  const {
    trendingTags,
    loading: tagsLoading,
    error: tagsError,
    fetchTrendingTags
  } = useTrendingTags(
    loadingState.phase === 'secondary' ||
    loadingState.phase === 'enhancement' ||
    loadingState.phase === 'complete',
    500
  );

  const {
    mostViewedNews,
    loading: mostViewedLoading,
    error: mostViewedError
  } = useMostViewedNewsThisWeek(
    10, // Get 10 articles for weekly popular news
    loadingState.phase === 'secondary' ||
    loadingState.phase === 'enhancement' ||
    loadingState.phase === 'complete'
  );

  // Enhancement content - load only after secondary content is ready
  const {
    videos: featuredVideos,
    loading: videosLoading,
    error: videosError
  } = useVideos(
    loadingState.phase === 'enhancement' ||
    loadingState.phase === 'complete'
  );

  const {
    images: galleryImages,
    loading: imagesLoading,
    error: imagesError
  } = useGalleryImages(
    loadingState.phase === 'enhancement' ||
    loadingState.phase === 'complete'
  );

  // Load ads with secondary content
  useAllAds(loadingState.phase !== 'critical');

  // Progressive loading management
  useEffect(() => {
    // Phase 1: Critical content loading
    if (loadingState.phase === 'critical' &&
      !criticalLoading &&
      (latestNewsData?.items?.length || featuredNews?.length)) {
      console.log('Critical content loaded, moving to secondary phase');
      setLoadingState(prev => ({
        ...prev,
        phase: 'secondary',
        criticalLoaded: true
      }));
    }
    // Phase 2: Secondary content loading
    else if (loadingState.phase === 'secondary' &&
      !categorizedLoading &&
      !mostViewedLoading &&
      categoriesLoaded) {
      console.log('Secondary content loaded, moving to enhancement phase');
      const timer = setTimeout(() => {
        setLoadingState(prev => ({
          ...prev,
          phase: 'enhancement',
          secondaryLoaded: true
        }));
      }, 100);
      return () => clearTimeout(timer);
    }
    // Phase 3: Enhancement content loading
    else if (loadingState.phase === 'enhancement' &&
      !videosLoading &&
      !imagesLoading) {
      console.log('Enhancement content loaded, loading complete');
      setLoadingState(prev => ({
        ...prev,
        phase: 'complete',
        enhancementLoaded: true
      }));
    }
  }, [
    loadingState.phase,
    criticalLoading,
    categorizedLoading,
    categoriesLoaded,
    mostViewedLoading,
    videosLoading,
    imagesLoading,
    latestNewsData?.items?.length,
    featuredNews?.length
  ]);

  // Load data for the selected category when component mounts
  useEffect(() => {
    if (loadingState.phase === 'secondary' ||
      loadingState.phase === 'enhancement' ||
      loadingState.phase === 'complete') {
      const lastSelectedCategory = getLastSelectedCategory();
      console.log(`🔄 Auto-loading data for last selected category: ${lastSelectedCategory}`);
      fetchCategoryNews(lastSelectedCategory);
    }
  }, [loadingState.phase, fetchCategoryNews]);

  // Set up intersection observer to detect when category section is visible
  useEffect(() => {
    if (!categorySectionRef.current || categories.length === 0) return;

    const loadedCategories = new Map<string, boolean>();
    categories.forEach(cat => loadedCategories.set(cat, false));

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            console.log('📍 Category section is visible, loading all categories');
            const defaultCategory = getLastSelectedCategory();
            fetchCategoryNews(defaultCategory);
            loadedCategories.set(defaultCategory, true);

            let delay = 300;
            categories.forEach(category => {
              if (!loadedCategories.get(category)) {
                setTimeout(() => {
                  console.log(`🔄 Auto-loading category: ${category} with delay ${delay}ms`);
                  fetchCategoryNews(category);
                  loadedCategories.set(category, true);
                }, delay);
                delay += 100;
              }
            });
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    observer.observe(categorySectionRef.current);

    return () => {
      observer.disconnect();
    };
  }, [categories, fetchCategoryNews]);

  // Infinite scroll for more news
  const {
    articles: infiniteData,
    loading: infiniteLoading,
    hasMore,
    loadMore
  } = useInfiniteScroll({
    pageSize: 15,
    enabled: loadingState.phase === 'secondary' ||
      loadingState.phase === 'enhancement' ||
      loadingState.phase === 'complete'
  });

  // Progressive translation - critical content shows original content initially
  const {
    translatedArticles: translatedLatestNews,
    enableProgressiveTranslation: enableLatestTranslation
  } = useTranslatedArticles(
    latestNewsData?.items || [],
    true,
    'critical',
    0
  );

  const {
    translatedArticles: translatedFeaturedNews,
    enableProgressiveTranslation: enableFeaturedTranslation
  } = useTranslatedArticles(
    featuredNews || [],
    true,
    'critical',
    0
  );

  // Get featured article - memoized to prevent re-renders
  const featuredArticle = useMemo(() =>
    translatedFeaturedNews[0] || translatedLatestNews[0],
    [translatedFeaturedNews, translatedLatestNews]
  );

  // Progressive translation for today's latest news
  const { translatedArticles: translatedTodayLatestNews } = useTranslatedArticles(
    todayLatestNews || [],
    true,
    'secondary',
    300
  );

  // Other articles (excluding featured)
  const otherArticles = useMemo(() => {
    const articlesToUse = translatedTodayLatestNews.length > 0
      ? translatedTodayLatestNews
      : translatedLatestNews;
    return articlesToUse.filter(article => article.id !== featuredArticle?.id);
  }, [translatedTodayLatestNews, translatedLatestNews, featuredArticle?.id]);

  // Enable translation for critical content after secondary phase starts
  useEffect(() => {
    if (loadingState.phase === 'secondary' ||
      loadingState.phase === 'enhancement' ||
      loadingState.phase === 'complete') {
      const timer = setTimeout(() => {
        console.log('🌍 Enabling translations for critical content');
        enableLatestTranslation();
        enableFeaturedTranslation();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [loadingState.phase, enableLatestTranslation, enableFeaturedTranslation]);

  // Infinite scroll observer
  useInfiniteScrollObserver(
    loadMore,
    hasMore,
    infiniteLoading || loadingState.phase === 'critical'
  );

  // Progressive loading states - memoized to prevent unnecessary re-renders
  const isInitialLoading = useMemo(() =>
    loadingState.phase === 'critical' && criticalLoading,
    [loadingState.phase, criticalLoading]
  );

  const isLoadingSecondary = useMemo(() =>
    loadingState.phase === 'secondary' &&
    (categorizedLoading || tagsLoading || mostViewedLoading),
    [loadingState.phase, categorizedLoading, tagsLoading, mostViewedLoading]
  );

  const isLoadingEnhancement = useMemo(() =>
    loadingState.phase === 'enhancement' && (videosLoading || imagesLoading),
    [loadingState.phase, videosLoading, imagesLoading]
  );

  // Debug loading phases
  useEffect(() => {
    console.log(`Current loading phase: ${loadingState.phase}`);
    console.log(`Loading states - Critical: ${criticalLoading}, Secondary: ${isLoadingSecondary}, Enhancement: ${isLoadingEnhancement}`);
  }, [loadingState.phase, criticalLoading, isLoadingSecondary, isLoadingEnhancement]);

  // Memoize the error retry handler
  const handleErrorRetry = useCallback(() => {
    window.location.reload();
  }, []);

  // Progressive loading states
  if (isInitialLoading) {
    return (
      <Layout>
        <div className="w-full max-w-none py-4 sm:py-6 px-2 sm:px-4 lg:px-6 xl:px-8">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 text-sm text-blue-800 flex items-center">
            <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
            <span>महत्वपूर्ण सामग्री लोड हो रही है...</span>
          </div>
          <ProgressiveHomeSkeleton phase="critical" />
        </div>
      </Layout>
    );
  }

  if (loadingState.phase === 'secondary' && isLoadingSecondary) {
    return (
      <Layout>
        <div className="w-full max-w-none py-4 sm:py-6 px-2 sm:px-4 lg:px-6 xl:px-8">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 text-sm text-blue-800 flex items-center">
            <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
            <span>अतिरिक्त सामग्री लोड हो रही है...</span>
          </div>
          <ProgressiveHomeSkeleton phase="secondary" />
        </div>
      </Layout>
    );
  }

  if (loadingState.phase === 'enhancement' && isLoadingEnhancement) {
    return (
      <Layout>
        <div className="w-full max-w-none py-4 sm:py-6 px-2 sm:px-4 lg:px-6 xl:px-8">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 text-sm text-blue-800 flex items-center">
            <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
            <span>अतिरिक्त मीडिया सामग्री लोड हो रही है...</span>
          </div>
          <ProgressiveHomeSkeleton phase="enhancement" />
        </div>
      </Layout>
    );
  }

  // Error state
  if (newsError || featuredError) {
    const error = newsError || featuredError || t('error_loading_data');
    const errorType = detectErrorType(error);
    const mappedErrorType = mapErrorType(errorType);

    return (
      <Layout>
        <div className="w-full max-w-none py-4 sm:py-6 px-2 sm:px-4 lg:px-6 xl:px-8">
          <AdaptiveErrorState
            error={error}
            errorType={mappedErrorType}
            onRetry={handleErrorRetry}
          />
        </div>
      </Layout>
    );
  }

  // Empty state
  if ((latestNewsData?.items?.length === 0 || !latestNewsData?.items) &&
    (featuredNews?.length === 0 || !featuredNews)) {
    return (
      <Layout>
        <div className="w-full max-w-none py-4 sm:py-6 px-2 sm:px-4 lg:px-6 xl:px-8">
          <AdaptiveEmptyState
            type="news"
            action={{ label: "पुनः प्रयास करें", onClick: handleErrorRetry }}
          />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* Header Advertisement */}
      <div className="w-full -mt-2">
        <AdContainer position="header" className="text-center" />
      </div>

      {/* Loading Phase Indicator */}
      {loadingState.phase !== 'complete' && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-2 mx-4 mb-2 text-xs text-blue-800">
          Loading Phase: {loadingState.phase}
          {isLoadingSecondary && " (Loading secondary content...)"}
          {isLoadingEnhancement && " (Loading enhancement content...)"}
        </div>
      )}

      <div className="w-full max-w-none py-4 sm:py-6 px-2 sm:px-4 lg:px-6 xl:px-8">

        {/* Hero Section */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 sm:gap-6 mb-8 sm:mb-10">
          {/* Main Featured Article */}
          <div className="lg:col-span-8">
            {featuredArticle ? (
              <NewsCardLarge
                id={featuredArticle.id}
                title={featuredArticle.title}
                excerpt={featuredArticle.excerpt}
                category={featuredArticle.category}
                imageUrl={featuredArticle.imageUrl}
                videoUrl={featuredArticle.videoUrl}
                publishedAt={featuredArticle.publishedAt}
                commentsCount={featuredArticle.commentsCount}
                featured={true}
              />
            ) : (
              <NewsCardSkeleton />
            )}
          </div>

          {/* Secondary Articles */}
          <div className="lg:col-span-4 space-y-4 sm:space-y-6">
            <div className="flex items-center justify-between mb-2">
              <h2 className="font-bold text-lg sm:text-xl">ताज़ा खबरें</h2>
              <Link to="/latest" className="text-xs sm:text-sm text-muted-foreground hover:text-primary flex items-center">
                और देखें <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4" />
              </Link>
            </div>

            <div className="space-y-4">
              {otherArticles.length > 0 ? (
                otherArticles.slice(0, 3).map((article: Article) => (
                  <NewsCardSmall
                    key={article.id}
                    id={article.id}
                    title={article.title}
                    imageUrl={article.imageUrl}
                    videoUrl={article.videoUrl}
                    publishedAt={article.publishedAt}
                  />
                ))
              ) : (
                Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="grid grid-cols-3 gap-4">
                    <div className="bg-muted rounded-md aspect-square"></div>
                    <div className="col-span-2 space-y-2">
                      <div className="h-4 bg-muted rounded"></div>
                      <div className="h-4 bg-muted rounded w-3/4"></div>
                      <div className="h-3 bg-muted rounded w-1/2"></div>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Trending Tags */}
            <div>
              <div className="mb-2">
                <h3 className="font-semibold">ट्रेंडिंग</h3>
              </div>
              <DataLoader
                data={trendingTags}
                isLoading={tagsLoading}
                error={tagsError}
                onRetry={fetchTrendingTags}
                loadingComponent={<TrendingTagsSkeleton />}
                contentType="tags"
                className="mt-2"
                compact={true}
              >
                {(tags: TrendingTag[]) => (
                  <div className="flex flex-wrap gap-2">
                    {tags.map((tag: TrendingTag) => (
                      <Link key={tag.name} to={`/tag/${encodeURIComponent(tag.name)}`}>
                        <Badge variant="secondary" className="hover:bg-primary hover:text-primary-foreground">
                          #{tag.name} ({tag.count})
                        </Badge>
                      </Link>
                    ))}
                  </div>
                )}
              </DataLoader>
            </div>
          </div>
        </div>

        {/* Category Tabs Section */}
        <div ref={categorySectionRef}>
          <Tabs
            defaultValue={getLastSelectedCategory()}
            onValueChange={(value: string) => {
              try {
                localStorage.setItem('lastSelectedCategory', value);
                console.log(`💾 Saved selected category to localStorage: ${value}`);
              } catch (err) {
                console.error('Error saving to localStorage:', err);
              }
            }}
          >
            <div className="mb-6 relative">
              <div className="overflow-x-auto pb-2 scrollbar-hide">
                <TabsList className="bg-transparent flex whitespace-nowrap">
                  {categories.map((category: string) => (
                    <TabsTrigger
                      key={category}
                      value={category}
                      className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                      onClick={() => {
                        console.log(`🔄 Loading category: ${category}`);
                        fetchCategoryNews(category);
                      }}
                      onMouseEnter={() => prefetchCategoryNews(category)}
                    >
                      {categoryDisplayNames[category]}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </div>
              <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-background to-transparent pointer-events-none"></div>
            </div>

            {categories.map((category: string) => (
              <TabsContent key={category} value={category}>
                <DataLoader
                  data={categorizedNews[category]}
                  isLoading={loadingCategories[category]}
                  error={categorizedError}
                  onRetry={() => fetchCategoryNews(category)}
                  loadingComponent={<CategoryContentSkeleton />}
                  contentType="category"
                  className="mb-6"
                >
                  {(categoryArticles: Article[]) => (
                    <div className="grid gap-4 sm:gap-6">
                      {categoryArticles.length > 0 ? (
                        categoryArticles.map((article: Article, index: number) => (
                          <div key={article.id}>
                            <NewsCardHorizontal
                              id={article.id}
                              title={article.title}
                              excerpt={article.excerpt}
                              category={categoryDisplayNames[article.category] || article.category}
                              imageUrl={article.imageUrl}
                              videoUrl={article.videoUrl}
                              publishedAt={article.publishedAt}
                              createdAt={article.createdAt}
                              embedVideo={false}
                            />
                            {index > 0 && (index + 1) % 2 === 0 && (
                              <div className="my-4 sm:my-6">
                                <AdContainer position="content" category={category} />
                              </div>
                            )}
                          </div>
                        ))
                      ) : (
                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
                          <div className="lg:col-span-2">
                            <AdaptiveEmptyState
                              type="category"
                              title={`${categoryDisplayNames[category]} में कोई समाचार नहीं`}
                              description="इस श्रेणी में अभी तक कोई समाचार उपलब्ध नहीं है।"
                              action={{ label: "सभी समाचार देखें", href: "/latest" }}
                            />
                          </div>

                          {/* Popular Articles Sidebar */}
                          <div className="lg:col-span-1">
                            <h3 className="text-xl font-bold mb-4">लोकप्रिय समाचार</h3>
                            <ProgressiveErrorBoundary
                              onError={(error: Error) => console.error("Popular articles error:", error)}
                              onRetry={() => window.location.reload()}
                              compact={true}
                              className="mb-4"
                            >
                              <DataLoader
                                data={mostViewedNews?.slice(0, 5)}
                                isLoading={mostViewedLoading}
                                error={mostViewedError}
                                onRetry={() => window.location.reload()}
                                loadingComponent={<PopularArticlesSkeleton />}
                                contentType="news"
                                className="space-y-4"
                              >
                                {(articles: Article[]) => (
                                  <div className="space-y-4">
                                    {articles.map((article: Article, index: number) => (
                                      <div key={article.id} className="group">
                                        <Link to={`/news/${article.id}`} className="flex items-start space-x-3 hover:bg-muted/50 p-2 rounded-lg transition-colors">
                                          <div className="bg-muted rounded-full w-6 h-6 flex items-center justify-center text-xs font-semibold flex-shrink-0">
                                            {index + 1}
                                          </div>
                                          <div className="flex-1">
                                            <h4 className="font-medium text-sm line-clamp-2 group-hover:text-primary">
                                              {article.title}
                                            </h4>
                                            <div className="flex items-center justify-between mt-1">
                                              <p className="text-xs text-muted-foreground">{article.category}</p>
                                              <span className="text-xs text-muted-foreground">
                                                {article.views || 0} views
                                              </span>
                                            </div>
                                          </div>
                                        </Link>
                                        {index < articles.length - 1 && <Separator className="my-2" />}
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </DataLoader>
                            </ProgressiveErrorBoundary>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </DataLoader>
                {categorizedNews[category] && categorizedNews[category].length > 0 && (
                  <div className="text-center mt-6">
                    <a
                      href={`/category/${getCategoryUrl(category)}`}
                      className="inline-flex items-center text-primary hover:underline"
                      onClick={(e) => {
                        // Force page refresh for category navigation
                        window.location.href = `/category/${getCategoryUrl(category)}`;
                      }}
                    >
                      और समाचार देखें <ChevronRight className="h-4 w-4 ml-1" />
                    </a>
                  </div>
                )}
              </TabsContent>
            ))}
          </Tabs>
        </div>

        {/* Popular Articles Section */}
        <div className="mb-8 sm:mb-10">
          <h2 className="text-xl sm:text-2xl font-bold mb-4 sm:mb-6">लोकप्रिय समाचार</h2>
          <ProgressiveErrorBoundary
            onError={(error: Error) => console.error("Popular articles error:", error)}
            onRetry={() => window.location.reload()}
            compact={true}
            className="mb-4"
          >
            <DataLoader
              data={mostViewedNews?.slice(0, 10)}
              isLoading={mostViewedLoading}
              error={mostViewedError}
              onRetry={() => window.location.reload()}
              loadingComponent={<PopularArticlesSkeleton />}
              contentType="news"
              className="space-y-4"
            >
              {(articles: Article[]) => (
                <div className="bg-card border rounded-lg p-4">
                  <div className="space-y-4">
                    {articles.map((article: Article, index: number) => (
                      <div key={article.id} className="group">
                        <Link to={`/news/${article.id}`} className="flex items-start space-x-4 hover:bg-muted/50 p-3 rounded-lg transition-colors">
                          <div className="bg-muted rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold flex-shrink-0">
                            {index + 1}
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold text-base line-clamp-2 group-hover:text-primary mb-1">
                              {article.title}
                            </h4>
                            <div className="flex items-center justify-between mt-1">
                              <p className="text-sm text-muted-foreground">{article.category}</p>
                              <span className="text-sm text-muted-foreground">
                                {article.views || 0} views
                              </span>
                            </div>
                          </div>
                        </Link>
                        {index < articles.length - 1 && <Separator />}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </DataLoader>
          </ProgressiveErrorBoundary>
        </div>



        {/* Two Column Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8">
          <div className="lg:col-span-2">
            {/* More News */}
            <div className="mb-6">
              <h2 className="text-xl sm:text-2xl font-bold mb-4">और समाचार</h2>
              <div className="space-y-4 sm:space-y-6">
                {infiniteData.map((article: Article) => (
                  <NewsCardHorizontal
                    key={article.id}
                    id={article.id}
                    title={article.title}
                    excerpt={article.excerpt}
                    category={article.category}
                    imageUrl={article.imageUrl}
                    videoUrl={article.videoUrl}
                    publishedAt={article.publishedAt}
                    createdAt={article.createdAt}
                    embedVideo={false}
                  />
                ))}
              </div>
            </div>


          </div>

          <div className="lg:col-span-1">
            {/* Advertisement */}
            <div className="mb-6">
              <AdContainer position="sidebar" />
            </div>

            {/* Popular Articles */}
            <div>
              <h3 className="text-xl font-bold mb-4">सबसे ज्यादा पढ़े गए</h3>
              <ProgressiveErrorBoundary
                onError={(error: Error) => console.error("Popular articles sidebar error:", error)}
                onRetry={() => window.location.reload()}
                compact={true}
                className="mb-4"
              >
                <DataLoader
                  data={mostViewedNews?.slice(0, 5)}
                  isLoading={mostViewedLoading}
                  error={mostViewedError}
                  onRetry={() => window.location.reload()}
                  loadingComponent={<PopularArticlesSkeleton />}
                  contentType="news"
                  className="space-y-4"
                >
                  {(articles: Article[]) => (
                    <div className="space-y-3">
                      {articles.map((article: Article, index: number) => (
                        <div key={article.id} className="group">
                          <Link to={`/news/${article.id}`} className="flex items-start space-x-3 hover:bg-muted/50 p-2 rounded-lg transition-colors">
                            <div className="bg-muted rounded-full w-6 h-6 flex items-center justify-center text-xs font-semibold flex-shrink-0">
                              {index + 1}
                            </div>
                            <div className="flex-1">
                              <h4 className="font-medium text-sm line-clamp-2 group-hover:text-primary">
                                {article.title}
                              </h4>
                              <div className="flex items-center justify-between mt-1">
                                <p className="text-xs text-muted-foreground">{article.category}</p>
                                <span className="text-xs text-muted-foreground">
                                  {article.views || 0} views
                                </span>
                              </div>
                            </div>
                          </Link>
                          {index < articles.length - 1 && <Separator className="my-2" />}
                        </div>
                      ))}
                    </div>
                  )}
                </DataLoader>
              </ProgressiveErrorBoundary>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}

// Helper function to map error types
function mapErrorType(errorType: 'network' | 'server' | 'timeout' | 'general' | string): "network" | "server" | "timeout" | "general" | "permission" | "notFound" | "validation" {
  switch (errorType) {
    case 'network':
      return 'network';
    case 'server':
      return 'server';
    case 'timeout':
      return 'timeout';
    case 'permission':
      return 'permission';
    case 'notFound':
      return 'notFound';
    case 'validation':
      return 'validation';
    default:
      return 'general';
  }
}
