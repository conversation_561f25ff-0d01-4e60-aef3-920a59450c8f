import React from 'react';
import { Play } from 'lucide-react';
import { ImageWithSkeleton } from '@/components/ui/image-skeleton';
import { SimpleYouTubeEmbed } from '@/components/media/SimpleYouTubeEmbed';

interface MediaDisplayCompactProps {
  imageUrl: string;
  videoUrl?: string;
  alt: string;
  aspectRatio?: 'square' | 'video' | 'portrait' | 'landscape';
  category?: string;
  width?: number;
  height?: number;
  className?: string;
  embedVideo?: boolean; // New prop to control embedding vs external link
}

export const MediaDisplayCompact: React.FC<MediaDisplayCompactProps> = ({
  imageUrl,
  videoUrl,
  alt,
  aspectRatio = 'landscape',
  category,
  width = 400,
  height = 300,
  className = '',
  embedVideo = false // Default to external link for compact view
}) => {
  // Function to check if URL is a YouTube URL
  const isYouTubeUrl = (url?: string): boolean => {
    if (!url) return false;
    return url.includes('youtube.com') || url.includes('youtu.be');
  };

  // Extract YouTube video ID for thumbnail generation
  const getYouTubeVideoId = (url: string): string | null => {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^#&?]*)/,
      /youtube\.com\/v\/([^#&?]*)/,
      /youtube\.com\/.*[?&]v=([^#&?]*)/
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match && match[1] && match[1].length === 11) {
        return match[1];
      }
    }

    return null;
  };

  const handlePlayClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (videoUrl) {
      // Open YouTube video in a new tab
      window.open(videoUrl, '_blank');
    }
  };

  // If we have a YouTube video URL and embedding is enabled, use the YouTube embed component
  if (videoUrl && isYouTubeUrl(videoUrl) && embedVideo) {
    return (
      <SimpleYouTubeEmbed
        videoUrl={videoUrl}
        thumbnailUrl={imageUrl}
        title={alt}
        aspectRatio={aspectRatio}
        className={className}
      />
    );
  }

  // Determine the image source - use YouTube thumbnail if it's a YouTube video
  let displayImageUrl = imageUrl;
  let fallbackImageUrl = imageUrl;

  if (videoUrl && isYouTubeUrl(videoUrl)) {
    const videoId = getYouTubeVideoId(videoUrl);
    if (videoId) {
      // Use YouTube thumbnail as primary image source, fallback to provided imageUrl
      displayImageUrl = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
      // If original imageUrl is empty or invalid, use standard quality YouTube thumbnail as fallback
      fallbackImageUrl = imageUrl || `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
    }
  }

  // Fallback to image with play button (default behavior for compact view)
  return (
    <div className={`relative ${className}`}>
      <ImageWithSkeleton
        src={displayImageUrl}
        alt={alt}
        aspectRatio={aspectRatio}
        category={category}
        width={width}
        height={height}
        className="w-full h-full object-cover"
        fallbackSrc={fallbackImageUrl}
      />

      {videoUrl && isYouTubeUrl(videoUrl) && (
        <button
          onClick={handlePlayClick}
          className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 hover:bg-opacity-40 transition-opacity"
          aria-label="Play video"
        >
          <div className="w-10 h-10 sm:w-12 sm:h-12 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center transition-colors">
            <Play className="w-5 h-5 sm:w-6 sm:h-6 text-white ml-0.5" fill="white" />
          </div>
        </button>
      )}
    </div>
  );
};