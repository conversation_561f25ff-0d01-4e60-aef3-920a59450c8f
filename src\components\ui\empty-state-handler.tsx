import React from 'react';
import { 
  EnhancedNewsEmptyState,
  EnhancedCategoryEmptyState,
  EnhancedSearchEmptyState,
  EnhancedVideosEmptyState,
  EnhancedGalleryEmptyState,
  EnhancedTagsEmptyState,
  EnhancedErrorState,
  EnhancedProgressiveError,
  PartialContentError
} from '@/components/ui/enhanced-empty-states';
import { detectErrorType } from '@/utils/errorUtils';
import { AdaptiveEmptyState, AdaptiveErrorState, PartialContentErrorState } from '@/components/ui/adaptive-empty-states';

/**
 * Component that handles empty states and errors in a consistent way
 * Shows appropriate empty state based on content type
 */
export interface EmptyStateHandlerProps {
  type: 'news' | 'category' | 'search' | 'video' | 'gallery' | 'tags' | 'comments' | 'generic';
  isLoading: boolean;
  isEmpty: boolean;
  error: string | null;
  onRetry?: () => void;
  category?: string;
  query?: string;
  onClear?: () => void;
  className?: string;
  compact?: boolean;
}

export const EmptyStateHandler: React.FC<EmptyStateHandlerProps> = ({
  type,
  isLoading,
  isEmpty,
  error,
  onRetry,
  category,
  query,
  onClear,
  className = '',
  compact = false
}) => {
  // If still loading, don't show empty state
  if (isLoading) {
    return null;
  }

  // If there's an error, show error state
  if (error) {
    const errorType = detectErrorType(error);
    const mappedErrorType = mapErrorType(errorType);
    
    if (compact) {
      return (
        <AdaptiveErrorState
          error={error}
          errorType={mappedErrorType}
          onRetry={onRetry}
          compact={true}
          className={className}
        />
      );
    }
    
    return (
      <AdaptiveErrorState
        error={error}
        errorType={mappedErrorType}
        onRetry={onRetry}
        className={className}
      />
    );
  }

  // If empty, show appropriate empty state
  if (isEmpty) {
    switch (type) {
      case 'news':
        return (
          <AdaptiveEmptyState
            type="news"
            action={onRetry ? { label: "पुनः प्रयास करें", onClick: onRetry } : { label: "होमपेज पर जाएं", href: "/" }}
            className={className}
            compact={compact}
          />
        );
      case 'category':
        return (
          <AdaptiveEmptyState
            type="category"
            title={category ? `${category} में कोई समाचार नहीं` : undefined}
            action={{ label: "सभी समाचार देखें", href: "/latest" }}
            secondaryAction={onRetry ? { label: "पुनः प्रयास करें", onClick: onRetry } : undefined}
            className={className}
            compact={compact}
          />
        );
      case 'search':
        return (
          <AdaptiveEmptyState
            type="search"
            title={query ? `"${query}" के लिए कोई परिणाम नहीं मिला` : undefined}
            action={onClear ? { label: "खोज साफ़ करें", onClick: onClear } : undefined}
            secondaryAction={{ label: "होमपेज पर जाएं", href: "/" }}
            className={className}
            compact={compact}
          />
        );
      case 'video':
        return (
          <AdaptiveEmptyState
            type="video"
            action={onRetry ? { label: "पुनः प्रयास करें", onClick: onRetry } : undefined}
            secondaryAction={{ label: "होमपेज पर जाएं", href: "/" }}
            className={className}
            compact={compact}
          />
        );
      case 'gallery':
        return (
          <AdaptiveEmptyState
            type="gallery"
            action={onRetry ? { label: "पुनः प्रयास करें", onClick: onRetry } : undefined}
            secondaryAction={{ label: "होमपेज पर जाएं", href: "/" }}
            className={className}
            compact={compact}
          />
        );
      case 'tags':
        return (
          <AdaptiveEmptyState
            type="tags"
            action={onRetry ? { label: "पुनः प्रयास करें", onClick: onRetry } : undefined}
            className={className}
            compact={compact}
          />
        );
      case 'comments':
        return (
          <AdaptiveEmptyState
            type="comments"
            className={className}
            compact={compact}
          />
        );
      case 'generic':
      default:
        return (
          <AdaptiveEmptyState
            type="generic"
            action={onRetry ? { label: "पुनः प्रयास करें", onClick: onRetry } : undefined}
            className={className}
            compact={compact}
          />
        );
    }
  }

  // If not loading, no error, and not empty, return null
  return null;
};

/**
 * Component that handles partial content errors
 * Shows a warning when some content loaded but other parts failed
 */
export const PartialContentHandler: React.FC<{
  error: string | null;
  message?: string;
  onRetry?: () => void;
  className?: string;
}> = ({ 
  error, 
  message = 'कुछ सामग्री लोड नहीं हो सकी। आप अभी भी उपलब्ध सामग्री देख सकते हैं।', 
  onRetry,
  className = '' 
}) => {
  if (!error) return null;
  
  return (
    <PartialContentErrorState
      message={message}
      onRetry={onRetry}
      className={className}
      severity="medium"
    />
  );
};

/**
 * Component that handles progressive error states
 * Shows different error states based on error type and severity
 */
export const ProgressiveErrorHandler: React.FC<{
  error: string | null;
  onRetry?: () => void;
  isCritical?: boolean;
  className?: string;
  compact?: boolean;
}> = ({ 
  error, 
  onRetry, 
  isCritical = false,
  className = '',
  compact = false
}) => {
  if (!error) return null;
  
  const errorType = detectErrorType(error);
  const mappedErrorType = mapErrorType(errorType);
  
  if (isCritical) {
    return (
      <AdaptiveErrorState
        error={error}
        errorType={mappedErrorType}
        onRetry={onRetry}
        className={className}
        compact={compact}
      />
    );
  }
  
  return (
    <PartialContentErrorState
      message={error}
      onRetry={onRetry}
      className={className}
      severity={getSeverityFromErrorType(errorType)}
    />
  );
};

// Helper functions
function mapErrorType(errorType: 'network' | 'server' | 'timeout' | 'general'): 'network' | 'server' | 'timeout' | 'permission' | 'notFound' | 'validation' | 'general' {
  switch (errorType) {
    case 'network':
      return 'network';
    case 'server':
      return 'server';
    case 'timeout':
      return 'timeout';
    default:
      return 'general';
  }
}

function getSeverityFromErrorType(errorType: 'network' | 'server' | 'timeout' | 'general'): 'low' | 'medium' | 'high' {
  switch (errorType) {
    case 'network':
      return 'medium';
    case 'server':
      return 'high';
    case 'timeout':
      return 'medium';
    default:
      return 'low';
  }
}