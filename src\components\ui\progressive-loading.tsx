import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  EnhancedNewsCardSkeleton, 
  EnhancedNewsCardHorizontalSkeleton,
  ShimmerSkeleton,
  PulsatingSkeletonLoader
} from '@/components/ui/enhanced-skeletons';
import { InfoBanner } from '@/components/ui/enhanced-empty-states';

/**
 * Progressive loading component that shows different loading states
 * based on the content type and loading phase
 */
export interface ProgressiveLoadingProps {
  type: 'card' | 'horizontal-card' | 'list' | 'grid' | 'content' | 'full-page';
  count?: number;
  message?: string;
  showMessage?: boolean;
  className?: string;
  phase?: 'critical' | 'secondary' | 'enhancement';
}

export const ProgressiveLoading: React.FC<ProgressiveLoadingProps> = ({
  type,
  count = 1,
  message = 'लोड हो रहा है...',
  showMessage = true,
  className = '',
  phase = 'critical'
}) => {
  // Determine if this is a critical loading state
  const isCritical = phase === 'critical';
  
  // Show appropriate loading indicator based on type
  const renderLoadingItem = () => {
    switch (type) {
      case 'card':
        return <EnhancedNewsCardSkeleton />;
      case 'horizontal-card':
        return <EnhancedNewsCardHorizontalSkeleton />;
      case 'list':
        return (
          <div className="space-y-2">
            <ShimmerSkeleton height="h-4" width="w-full" />
            <ShimmerSkeleton height="h-4" width="w-4/5" />
            <ShimmerSkeleton height="h-4" width="w-2/3" />
          </div>
        );
      case 'grid':
        return (
          <div className="grid grid-cols-2 gap-2">
            <ShimmerSkeleton height="h-24" width="w-full" />
            <ShimmerSkeleton height="h-24" width="w-full" />
            <ShimmerSkeleton height="h-24" width="w-full" />
            <ShimmerSkeleton height="h-24" width="w-full" />
          </div>
        );
      case 'content':
        return (
          <div className="space-y-4">
            <ShimmerSkeleton height="h-6" width="w-3/4" />
            <ShimmerSkeleton height="h-4" width="w-full" />
            <ShimmerSkeleton height="h-4" width="w-full" />
            <ShimmerSkeleton height="h-4" width="w-4/5" />
            <ShimmerSkeleton height="h-4" width="w-full" />
          </div>
        );
      case 'full-page':
        return <PulsatingSkeletonLoader message={message} />;
      default:
        return <Skeleton className="h-12 w-full" />;
    }
  };

  return (
    <div className={className}>
      {showMessage && isCritical && (
        <InfoBanner 
          message={message} 
          className="mb-4"
        />
      )}
      <div className={`space-y-4 ${className}`}>
        {Array.from({ length: count }).map((_, index) => (
          <div key={index}>
            {renderLoadingItem()}
          </div>
        ))}
      </div>
    </div>
  );
};

/**
 * Component that shows a content-aware skeleton loader
 * that matches the shape of the expected content
 */
export const ContentAwareSkeleton: React.FC<{
  contentType: 'article' | 'video' | 'gallery' | 'comment' | 'sidebar';
  className?: string;
}> = ({ contentType, className = '' }) => {
  switch (contentType) {
    case 'article':
      return (
        <div className={`space-y-4 ${className}`}>
          <ShimmerSkeleton height="h-8" width="w-3/4" className="mb-2" />
          <ShimmerSkeleton height="h-48" width="w-full" className="mb-4" />
          <ShimmerSkeleton height="h-4" width="w-full" />
          <ShimmerSkeleton height="h-4" width="w-full" />
          <ShimmerSkeleton height="h-4" width="w-4/5" />
          <ShimmerSkeleton height="h-4" width="w-full" />
          <ShimmerSkeleton height="h-4" width="w-3/4" />
        </div>
      );
    case 'video':
      return (
        <div className={`space-y-2 ${className}`}>
          <ShimmerSkeleton height="h-6" width="w-1/3" className="mb-2" />
          <ShimmerSkeleton height="h-64" width="w-full" className="aspect-video" />
          <ShimmerSkeleton height="h-5" width="w-2/3" className="mt-2" />
          <ShimmerSkeleton height="h-4" width="w-1/3" />
        </div>
      );
    case 'gallery':
      return (
        <div className={`${className}`}>
          <ShimmerSkeleton height="h-6" width="w-1/3" className="mb-4" />
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <ShimmerSkeleton height="h-40" width="w-full" className="aspect-square" />
            <ShimmerSkeleton height="h-40" width="w-full" className="aspect-square" />
            <ShimmerSkeleton height="h-40" width="w-full" className="aspect-square" />
            <ShimmerSkeleton height="h-40" width="w-full" className="aspect-square" />
            <ShimmerSkeleton height="h-40" width="w-full" className="aspect-square" />
            <ShimmerSkeleton height="h-40" width="w-full" className="aspect-square" />
          </div>
        </div>
      );
    case 'comment':
      return (
        <div className={`border rounded-lg p-4 ${className}`}>
          <div className="flex items-start gap-3 mb-3">
            <ShimmerSkeleton height="h-8" width="w-8" className="rounded-full" />
            <div className="flex-1">
              <ShimmerSkeleton height="h-4" width="w-32" className="mb-1" />
              <ShimmerSkeleton height="h-3" width="w-24" />
            </div>
          </div>
          <ShimmerSkeleton height="h-4" width="w-full" />
          <ShimmerSkeleton height="h-4" width="w-4/5" />
        </div>
      );
    case 'sidebar':
      return (
        <div className={`space-y-4 ${className}`}>
          <ShimmerSkeleton height="h-6" width="w-1/2" className="mb-2" />
          <div className="space-y-3">
            <div className="flex gap-3">
              <ShimmerSkeleton height="h-16" width="w-16" className="rounded" />
              <div className="flex-1">
                <ShimmerSkeleton height="h-4" width="w-full" />
                <ShimmerSkeleton height="h-4" width="w-2/3" className="mt-1" />
                <ShimmerSkeleton height="h-3" width="w-1/3" className="mt-2" />
              </div>
            </div>
            <div className="flex gap-3">
              <ShimmerSkeleton height="h-16" width="w-16" className="rounded" />
              <div className="flex-1">
                <ShimmerSkeleton height="h-4" width="w-full" />
                <ShimmerSkeleton height="h-4" width="w-2/3" className="mt-1" />
                <ShimmerSkeleton height="h-3" width="w-1/3" className="mt-2" />
              </div>
            </div>
            <div className="flex gap-3">
              <ShimmerSkeleton height="h-16" width="w-16" className="rounded" />
              <div className="flex-1">
                <ShimmerSkeleton height="h-4" width="w-full" />
                <ShimmerSkeleton height="h-4" width="w-2/3" className="mt-1" />
                <ShimmerSkeleton height="h-3" width="w-1/3" className="mt-2" />
              </div>
            </div>
          </div>
        </div>
      );
    default:
      return <Skeleton className="h-12 w-full" />;
  }
};

/**
 * Component that shows a loading state with a message
 * that can be used when no skeleton is appropriate
 */
export const LoadingMessage: React.FC<{
  message?: string;
  className?: string;
}> = ({ message = 'लोड हो रहा है...', className = '' }) => {
  return (
    <div className={`flex flex-col items-center justify-center py-6 ${className}`}>
      <div className="relative w-12 h-12 mb-3">
        <div className="absolute inset-0 rounded-full border-4 border-muted"></div>
        <div className="absolute inset-0 rounded-full border-4 border-t-primary animate-spin"></div>
      </div>
      <p className="text-muted-foreground text-sm">{message}</p>
    </div>
  );
};