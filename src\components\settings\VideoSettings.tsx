import React, { useState, useEffect } from 'react';
import { Settings, Play, ExternalLink } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { getVideoSettings, saveVideoSettings, resetVideoSettings, VideoSettings as VideoSettingsType } from '@/utils/videoSettings';

interface VideoSettingsProps {
  onSettingsChange?: (settings: VideoSettingsType) => void;
}

export const VideoSettings: React.FC<VideoSettingsProps> = ({ onSettingsChange }) => {
  const [settings, setSettings] = useState<VideoSettingsType>(getVideoSettings());
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const currentSettings = getVideoSettings();
    setSettings(currentSettings);
  }, []);

  const handleSettingChange = (key: keyof VideoSettingsType, value: boolean) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    saveVideoSettings({ [key]: value });
    onSettingsChange?.(newSettings);
  };

  const handleReset = () => {
    resetVideoSettings();
    const defaultSettings = getVideoSettings();
    setSettings(defaultSettings);
    onSettingsChange?.(defaultSettings);
  };

  if (!isOpen) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 z-40 shadow-lg"
      >
        <Settings className="w-4 h-4 mr-2" />
        Video Settings
      </Button>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="w-5 h-5" />
            Video Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="embed-articles">Embed in Articles</Label>
                <p className="text-sm text-muted-foreground">
                  Play videos directly in news articles
                </p>
              </div>
              <Switch
                id="embed-articles"
                checked={settings.embedInArticles}
                onCheckedChange={(checked) => handleSettingChange('embedInArticles', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="embed-cards">Embed in News Cards</Label>
                <p className="text-sm text-muted-foreground">
                  Play videos directly in news card previews
                </p>
              </div>
              <Switch
                id="embed-cards"
                checked={settings.embedInCards}
                onCheckedChange={(checked) => handleSettingChange('embedInCards', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="embed-gallery">Embed in Gallery</Label>
                <p className="text-sm text-muted-foreground">
                  Play videos directly in photo gallery
                </p>
              </div>
              <Switch
                id="embed-gallery"
                checked={settings.embedInGallery}
                onCheckedChange={(checked) => handleSettingChange('embedInGallery', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="auto-play">Auto Play</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically start playing videos when opened
                </p>
              </div>
              <Switch
                id="auto-play"
                checked={settings.autoPlay}
                onCheckedChange={(checked) => handleSettingChange('autoPlay', checked)}
              />
            </div>
          </div>

          <div className="flex gap-2 pt-4">
            <Button
              variant="outline"
              onClick={handleReset}
              className="flex-1"
            >
              Reset to Default
            </Button>
            <Button
              onClick={() => setIsOpen(false)}
              className="flex-1"
            >
              Close
            </Button>
          </div>

          <div className="text-xs text-muted-foreground text-center pt-2">
            <p className="flex items-center justify-center gap-1">
              <ExternalLink className="w-3 h-3" />
              When disabled, videos open in new tab
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};